#!/usr/bin/env python3
"""
Transit Scenario Copilot - APTA.com Conference Demo
==================================================

A multi-agent dashboard for transit planning and scenario analysis.

Agent Architecture:
    ┌─────────────────┐   ┌──────────────┐   ┌──────────────┐   ┌──────────┐
    │ RIDERSHIP       │──▶│ SIMULATION   │──▶│ RECOMMEND    │──▶│ VISUAL   │
    │ ANALYTICS       │   │ AGENT        │   │ AGENT        │   │ AGENT    │
    └─────────────────┘   └──────────────┘   └──────────────┘   └──────────┘
           ▲                      ▲                  ▲               ▲
           │                      │                  │               │
      GTFS + CSV           Elasticities +      Impact data +   Dashboard UI
                            Scenario          Budget constraints
"""

import os
import re
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import warnings
warnings.filterwarnings('ignore')

# Core dependencies
import pandas as pd
import numpy as np
import gradio as gr
from dotenv import load_dotenv

# Visualization libraries
import plotly.express as px
import plotly.graph_objects as go

# AutoGen for agent framework
try:
    import autogen
    AUTOGEN_AVAILABLE = True
except ImportError:
    AUTOGEN_AVAILABLE = False
    print("Warning: AutoGen not available. Using simplified agent structure.")

# Load environment variables from .env file
load_dotenv()

# LLM setup - we'll use OpenAI but mock if not available
try:
    import openai
    OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
    LLM_AVAILABLE = bool(OPENAI_API_KEY)
except ImportError:
    LLM_AVAILABLE = False


@dataclass
class Config:
    """Central config for the Transit Scenario Copilot"""
    output_dir: Path = Path("./outputs")
    timestamp: str = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Default demo data paths
    default_ridership: str = "upt_prediction3.csv"
    default_elasticities: str = "elasticities.csv"
    
    # LLM settings
    llm_model: str = "gpt-4o"
    llm_temperature: float = 0.3
    
    # Simulation defaults
    default_elasticity_fare: float = -0.3
    default_elasticity_frequency: float = 0.4
    
    # APTA theme colors
    colors = {
        'primary': '#0055A4',
        'secondary': '#D13239',
        'accent1': '#00A651',
        'accent2': '#F7941D',
        'accent3': '#662D91',
        'background': '#F5F7FA',
        'text': '#333333',
    }


def setup_output_dir(config: Config) -> Path:
    """Create timestamped output directory"""
    output_path = config.output_dir / config.timestamp
    output_path.mkdir(parents=True, exist_ok=True)
    print(f"✓ Output directory: {output_path}")
    return output_path


def parse_scenario_text(scenario_text: str) -> List[Dict[str, Any]]:
    """
    Parse natural language scenario into structured format.
    Example: "increase rail Red Line frequency 20% off-peak ; cut weekend bus fares 25%"
    """
    scenarios = []
    
    # Split by semicolon for multiple changes
    changes = scenario_text.split(';')
    
    for change in changes:
        change = change.strip().lower()
        scenario = {}
        
        # Detect action (increase/decrease/cut)
        if 'increase' in change:
            scenario['action'] = 'increase'
        elif 'decrease' in change or 'cut' in change or 'reduce' in change:
            scenario['action'] = 'decrease'
        else:
            continue
        
        # Detect mode (rail/bus)
        if 'rail' in change:
            scenario['mode'] = 'rail'
        elif 'bus' in change:
            scenario['mode'] = 'bus'
        else:
            scenario['mode'] = 'all'
        
        # Detect what's changing (frequency/fare)
        if 'frequency' in change or 'headway' in change or 'service' in change:
            scenario['variable'] = 'frequency'
        elif 'fare' in change or 'price' in change or 'cost' in change:
            scenario['variable'] = 'fare'
        
        # Extract percentage (look for number followed by %)
        pct_match = re.search(r'(\d+)%', change)
        if pct_match:
            scenario['percent_change'] = float(pct_match.group(1)) / 100
            if scenario['action'] == 'decrease':
                scenario['percent_change'] *= -1
        
        # Extract route if specified
        if 'red line' in change:
            scenario['route'] = 'Red Line'
        elif 'blue line' in change:
            scenario['route'] = 'Blue Line'
        
        # Extract time period
        if 'off-peak' in change or 'offpeak' in change:
            scenario['time_period'] = 'off-peak'
        elif 'peak' in change:
            scenario['time_period'] = 'peak'
        elif 'weekend' in change:
            scenario['time_period'] = 'weekend'
        else:
            scenario['time_period'] = 'all'
        
        if 'variable' in scenario and 'percent_change' in scenario:
            scenarios.append(scenario)
    
    return scenarios


def mock_llm_response(prompt: str) -> str:
    """Fallback when no LLM is available"""
    if "recovery" in prompt.lower():
        return "Transit ridership shows strong recovery patterns, with rail at 78% and bus at 65% of pre-COVID levels."
    elif "recommend" in prompt.lower():
        return "The optimal scenario balances ridership gains with revenue sustainability. Focus on high-elasticity routes."
    else:
        return "Analysis complete. Key findings identified in the data."


def call_llm(prompt: str, config: Config) -> str:
    """Call LLM with fallback to mock"""
    if not LLM_AVAILABLE:
        return mock_llm_response(prompt)
    
    try:
        client = openai.OpenAI(api_key=OPENAI_API_KEY)
        response = client.chat.completions.create(
            model=config.llm_model,
            messages=[{"role": "user", "content": prompt}],
            temperature=config.llm_temperature
        )
        return response.choices[0].message.content
    except Exception as e:
        print(f"LLM call failed: {e}. Using mock response.")
        return mock_llm_response(prompt)


def ridership_analytics_agent(
    ridership_csv: Optional[Path] = None,
    question: Optional[str] = None,
    config: Config = Config()
) -> Dict[str, Any]:
    """
    Agent 1: Analyze current ridership patterns and recovery rates.
    Returns recovery percentages by mode/route and answers NL questions.
    """
    print("\n🔍 RIDERSHIP ANALYTICS AGENT")
    
    # Load ridership data
    if ridership_csv and Path(ridership_csv).exists():
        df = pd.read_csv(ridership_csv)
        print(f"  → Loaded {len(df):,} ridership records")
    else:
        # Create mock data if file doesn't exist
        print("  → Creating mock ridership data")
        dates = pd.date_range('2024-01-01', periods=52, freq='W')
        modes = ['Rail', 'Bus']
        df = pd.DataFrame({
            'Week of': np.repeat(dates, len(modes)),
            'Mode': modes * len(dates),
            'Prediction': np.random.randint(50000, 200000, size=len(dates) * len(modes)),
            'Comparison Pre-Covid Prediction': np.random.randint(80000, 250000, size=len(dates) * len(modes))
        })
    
    # Extract mode information
    if 'Mode' not in df.columns:
        # Try to derive mode from Name column
        if 'Name' in df.columns:
            df['Mode'] = 'Bus'  # Default to bus
            rail_keywords = ['rail', 'metro', 'subway', 'train', 'transit']
            for keyword in rail_keywords:
                df.loc[df['Name'].str.lower().str.contains(keyword, na=False), 'Mode'] = 'Rail'
        else:
            # Create a mock Mode column
            df['Mode'] = np.random.choice(['Bus', 'Rail'], size=len(df))
    
    # Calculate recovery percentages
    df['recovery_pct'] = (df['Prediction'] / df['Comparison Pre-Covid Prediction']) * 100
    
    # Aggregate by mode
    recovery_by_mode = df.groupby('Mode')[['Prediction', 'Comparison Pre-Covid Prediction', 'recovery_pct']].mean()
    recovery_by_mode = recovery_by_mode.round(1)
    
    # Generate summary
    summary_parts = []
    for mode, row in recovery_by_mode.iterrows():
        summary_parts.append(
            f"{mode}: {row['recovery_pct']:.1f}% recovered "
            f"({row['Prediction']:,.0f} vs {row['Comparison Pre-Covid Prediction']:,.0f} pre-COVID)"
        )
    
    base_summary = "Ridership Recovery Status:\n" + "\n".join(summary_parts)
    
    # Handle natural language questions
    if question:
        prompt = f"""
        Transit ridership data summary:
        {base_summary}
        
        Question: {question}
        
        Provide a concise, data-driven answer.
        """
        nl_answer = call_llm(prompt, config)
        summary_txt = f"{base_summary}\n\nQ: {question}\nA: {nl_answer}"
    else:
        summary_txt = base_summary
    
    print(f"\n  ✓ Recovery analysis complete")
    
    return {
        'recovery_df': recovery_by_mode,
        'summary_txt': summary_txt,
        'full_df': df
    }


def simulation_agent(
    scenario_json: List[Dict[str, Any]],
    recovery_df: pd.DataFrame,
    elasticities_path: Optional[Path] = None,
    config: Config = Config()
) -> Dict[str, Any]:
    """
    Agent 2: Apply elasticity-based simulation to scenarios.
    Calculate ridership & revenue impacts, vehicle-hour requirements.
    """
    print("\n⚡ SIMULATION AGENT")
    
    # Load or create elasticities
    if elasticities_path and Path(elasticities_path).exists():
        elasticities = pd.read_csv(elasticities_path)
    else:
        # Default elasticities
        elasticities = pd.DataFrame({
            'mode': ['rail', 'bus'],
            'fare_elasticity': [-0.3, -0.4],  # More elastic for bus
            'frequency_elasticity': [0.4, 0.5]  # More responsive for bus
        })
    
    # Prepare results
    impacts = []
    
    for scenario in scenario_json:
        print(f"  → Simulating: {scenario}")
        
        mode = scenario.get('mode', 'all')
        variable = scenario.get('variable', 'fare')
        pct_change = scenario.get('percent_change', 0)
        
        # Get appropriate elasticity
        if mode == 'all':
            modes_to_simulate = ['rail', 'bus']
        else:
            modes_to_simulate = [mode]
        
        for sim_mode in modes_to_simulate:
            # Get elasticity value
            mode_elasticity = elasticities[elasticities['mode'] == sim_mode]
            if mode_elasticity.empty:
                elasticity_value = config.default_elasticity_fare if variable == 'fare' else config.default_elasticity_frequency
            else:
                elasticity_value = mode_elasticity[f'{variable}_elasticity'].iloc[0]
            
            # Calculate ridership impact
            ridership_pct_change = elasticity_value * pct_change * 100
            
            # Get baseline from recovery_df
            sim_mode_title = sim_mode.title()
            base_ridership = recovery_df.loc[sim_mode_title, 'Prediction'] if sim_mode_title in recovery_df.index else 100000
            new_ridership = base_ridership * (1 + ridership_pct_change / 100)
            
            # Revenue calculations (simplified)
            if variable == 'fare':
                revenue_pct_change = (1 + pct_change) * (1 + ridership_pct_change / 100) - 1
            else:
                revenue_pct_change = ridership_pct_change / 100
            
            # Vehicle-hour requirements (for frequency changes)
            if variable == 'frequency':
                vehicle_hours_change = pct_change
            else:
                vehicle_hours_change = 0
            
            # Crowding proxy (simplified)
            crowding_index = new_ridership / (base_ridership * (1 + vehicle_hours_change))
            
            impacts.append({
                'scenario': f"{scenario['action']} {sim_mode} {variable} {abs(pct_change)*100:.0f}%",
                'mode': sim_mode,
                'variable': variable,
                'pct_change': pct_change,
                'ridership_impact_pct': ridership_pct_change,
                'revenue_impact_pct': revenue_pct_change * 100,
                'vehicle_hours_change_pct': vehicle_hours_change * 100,
                'crowding_index': crowding_index,
                'new_ridership': new_ridership,
                'base_ridership': base_ridership
            })
    
    impact_df = pd.DataFrame(impacts)
    
    print(f"\n  ✓ Simulated {len(impacts)} scenarios")
    
    return {
        'impact_df': impact_df,
        'elasticities_used': elasticities
    }


def recommendation_agent(
    impact_df: pd.DataFrame,
    budget_cap: Optional[float] = None,
    optimize: bool = False,
    config: Config = Config()
) -> Dict[str, Any]:
    """
    Agent 3: Rank scenarios and generate executive recommendations.
    Can brute-force search if optimize=True.
    """
    print("\n💡 RECOMMENDATION AGENT")
    
    if impact_df.empty:
        return {
            'top_n_df': pd.DataFrame(),
            'exec_summary': "No scenarios to evaluate."
        }
    
    # Calculate composite score
    impact_df['composite_score'] = (
        impact_df['ridership_impact_pct'] * 0.5 +
        impact_df['revenue_impact_pct'] * 0.3 +
        (-impact_df['vehicle_hours_change_pct'] * 0.2)
    )
    
    # Apply budget constraints if provided
    if budget_cap:
        impact_df['estimated_cost_m'] = impact_df['vehicle_hours_change_pct'] * 0.1
        impact_df = impact_df[impact_df['estimated_cost_m'] <= budget_cap]
    
    # Sort by composite score
    top_scenarios = impact_df.nlargest(3, 'composite_score')[
        ['scenario', 'ridership_impact_pct', 'revenue_impact_pct', 'composite_score']
    ].round(1)
    
    # Generate executive summary
    if len(top_scenarios) > 0:
        top_scenario = top_scenarios.iloc[0]
        
        prompt = f"""
        Generate a 3-sentence executive summary for this transit scenario recommendation:
        
        Top scenario: {top_scenario['scenario']}
        Ridership impact: {top_scenario['ridership_impact_pct']:.1f}%
        Revenue impact: {top_scenario['revenue_impact_pct']:.1f}%
        
        Make it concise, action-oriented, and highlight the key benefit.
        """
        
        exec_summary = call_llm(prompt, config)
    else:
        exec_summary = "No viable scenarios found within constraints."
    
    print("  ✓ Generated recommendations")
    
    return {
        'top_n_df': top_scenarios,
        'exec_summary': exec_summary,
        'full_impact_df': impact_df
    }


def visualization_agent(
    ridership_results: Dict[str, Any],
    simulation_results: Dict[str, Any],
    recommendation_results: Dict[str, Any],
    config: Config = Config()
) -> Dict[str, Any]:
    """
    Agent 4: Create visualizations for dashboard display with error handling.
    """
    print("\n📊 VISUALIZATION AGENT")
    
    visualizations = {}
    
    try:
        # 1. Ridership Recovery Visualization
        if ridership_results and 'recovery_df' in ridership_results and not ridership_results['recovery_df'].empty:
            recovery_df = ridership_results['recovery_df']
            
            fig_recovery = go.Figure()
            
            colors = [config.colors['primary'], config.colors['secondary']]
            
            for i, mode in enumerate(recovery_df.index):
                color = colors[i % len(colors)]
                fig_recovery.add_trace(go.Bar(
                    x=[mode],
                    y=[recovery_df.loc[mode, 'recovery_pct']],
                    name=mode,
                    marker_color=color,
                    text=[f"{recovery_df.loc[mode, 'recovery_pct']:.1f}%"],
                    textposition='auto',
                    showlegend=False
                ))
            
            fig_recovery.update_layout(
                title='Ridership Recovery by Mode',
                xaxis_title='Mode',
                yaxis_title='Recovery Percentage (%)',
                yaxis=dict(range=[0, 110]),
                plot_bgcolor=config.colors['background'],
                font=dict(color=config.colors['text']),
                height=400,
                margin=dict(l=50, r=50, t=50, b=50)
            )
            
            visualizations['recovery_chart'] = fig_recovery
        
        # 2. Simulation Impact Visualization
        if simulation_results and 'impact_df' in simulation_results and not simulation_results['impact_df'].empty:
            impact_df = simulation_results['impact_df']
            
            fig_impact = go.Figure()
            
            colors = [config.colors['primary'], config.colors['secondary'], 
                     config.colors['accent1'], config.colors['accent2']]
            
            for i, (_, row) in enumerate(impact_df.iterrows()):
                color = colors[i % len(colors)]
                
                fig_impact.add_trace(go.Bar(
                    x=[row['scenario']],
                    y=[row['ridership_impact_pct']],
                    name=row['scenario'],
                    marker_color=color,
                    text=[f"{row['ridership_impact_pct']:.1f}%"],
                    textposition='auto',
                    showlegend=False
                ))
            
            fig_impact.update_layout(
                title='Ridership Impact by Scenario',
                xaxis_title='Scenario',
                yaxis_title='Ridership Change (%)',
                plot_bgcolor=config.colors['background'],
                font=dict(color=config.colors['text']),
                height=400,
                margin=dict(l=50, r=50, t=50, b=50)
            )
            
            visualizations['impact_chart'] = fig_impact
        
        # 3. Recommendation Visualization
        if recommendation_results and 'top_n_df' in recommendation_results and not recommendation_results['top_n_df'].empty:
            top_scenarios = recommendation_results['top_n_df']
            
            fig_top = go.Figure()
            
            colors = [config.colors['primary'], config.colors['accent1'], config.colors['accent2']]
            
            for i, (_, row) in enumerate(top_scenarios.iterrows()):
                color = colors[i % len(colors)]
                
                fig_top.add_trace(go.Bar(
                    y=[row['scenario']],
                    x=[row['composite_score']],
                    orientation='h',
                    name=row['scenario'],
                    marker_color=color,
                    text=[f"Score: {row['composite_score']:.1f}"],
                    textposition='auto',
                    showlegend=False
                ))
            
            fig_top.update_layout(
                title='Top Recommended Scenarios',
                xaxis_title='Composite Score',
                yaxis_title='Scenario',
                plot_bgcolor=config.colors['background'],
                font=dict(color=config.colors['text']),
                height=400,
                margin=dict(l=50, r=50, t=50, b=50)
            )
            
            visualizations['top_scenarios_chart'] = fig_top
        
    except Exception as e:
        print(f"Error in visualization_agent: {str(e)}")
        fig_error = go.Figure()
        fig_error.add_annotation(
            text=f"Visualization Error: {str(e)}",
            xref="paper", yref="paper",
            x=0.5, y=0.5,
            showarrow=False
        )
        fig_error.update_layout(
            title="Visualization Error",
            height=300
        )
        visualizations['error_chart'] = fig_error
    
    print(f"  ✓ Generated {len(visualizations)} visualizations")
    
    return visualizations


def create_dashboard(config: Config):
    """Create Gradio dashboard interface with proper state management"""
    
    # Custom CSS for APTA theme
    css = f"""
    .gradio-container {{
        background-color: {config.colors['background']};
    }}
    .tabs {{
        border-color: {config.colors['primary']};
    }}
    .tab-selected {{
        background-color: {config.colors['primary']};
        color: white;
    }}
    h1, h2, h3 {{
        color: {config.colors['primary']};
    }}
    """
    
    # Dashboard title and description
    description = f"""
    <div style="text-align: center; margin-bottom: 20px;">
        <h1 style="color: {config.colors['primary']};">Transit Scenario Copilot</h1>
        <h3 style="color: {config.colors['secondary']};">APTA.com Conference Demo</h3>
        <p>A multi-agent AI system for transit planning and scenario analysis</p>
    </div>
    """
    
    # Define dashboard layout
    with gr.Blocks(css=css, title="Transit Scenario Copilot") as dashboard:
        gr.HTML(description)
        
        # Initialize shared state
        state = gr.State({
            "ridership_results": None,
            "simulation_results": None,
            "recommendation_results": None,
            "visualizations": None,
            "config": config
        })
        
        # Main tabs
        with gr.Tabs():
            # Tab 1: Data Input & Ridership Analysis
            with gr.Tab("1️⃣ Ridership Analysis"):
                with gr.Row():
                    with gr.Column(scale=1):
                        ridership_file = gr.File(
                            label="Upload Ridership Data (CSV)",
                            file_types=[".csv"],
                            value=None
                        )
                        ridership_question = gr.Textbox(
                            label="Ask a question about ridership (optional)",
                            placeholder="e.g., Which regions show the strongest recovery?",
                            value=""
                        )
                        analyze_btn = gr.Button("Analyze Ridership", variant="primary")
                    
                    with gr.Column(scale=2):
                        ridership_summary = gr.Textbox(
                            label="Ridership Analysis Summary",
                            placeholder="Analysis results will appear here...",
                            lines=10,
                            interactive=False,
                            value=""
                        )
                
                with gr.Row():
                    with gr.Column():
                        ridership_chart = gr.Plot(label="Ridership Recovery Chart", value=None)
                    with gr.Column():
                        region_chart = gr.Plot(label="Regional Recovery Chart", value=None)
            
            # Tab 2: Scenario Simulation
            with gr.Tab("2️⃣ Scenario Simulation"):
                with gr.Row():
                    with gr.Column(scale=1):
                        scenario_input = gr.Textbox(
                            label="Enter Scenario (Natural Language)",
                            placeholder="e.g., increase rail frequency 20% off-peak; cut weekend bus fares 25%",
                            lines=3,
                            value=""
                        )
                        elasticity_file = gr.File(
                            label="Upload Elasticities (CSV, optional)",
                            file_types=[".csv"],
                            value=None
                        )
                        simulate_btn = gr.Button("Simulate Scenario", variant="primary")
                    
                    with gr.Column(scale=2):
                        scenario_results = gr.Dataframe(
                            label="Scenario Simulation Results",
                            headers=["scenario", "mode", "variable", "ridership_impact_pct", "revenue_impact_pct"],
                            interactive=False,
                            value=None
                        )
                
                with gr.Row():
                    with gr.Column():
                        impact_chart = gr.Plot(label="Ridership Impact Chart", value=None)
                    with gr.Column():
                        radar_chart = gr.Plot(label="Multi-dimensional Comparison", value=None)
            
            # Tab 3: Recommendations
            with gr.Tab("3️⃣ Recommendations"):
                with gr.Row():
                    with gr.Column(scale=1):
                        budget_cap = gr.Number(
                            label="Budget Cap ($ millions, optional)",
                            value=None
                        )
                        optimize_cb = gr.Checkbox(
                            label="Run Optimization",
                            value=False
                        )
                        recommend_btn = gr.Button("Generate Recommendations", variant="primary")
                    
                    with gr.Column(scale=2):
                        exec_summary = gr.Textbox(
                            label="Executive Summary",
                            placeholder="Recommendations will appear here...",
                            lines=6,
                            interactive=False,
                            value=""
                        )
                
                with gr.Row():
                    top_scenarios_chart = gr.Plot(label="Top Recommended Scenarios", value=None)
        
        # Define event handlers with proper error handling
        def analyze_ridership(ridership_path, question, state_dict):
            """Handle ridership analysis button click with error handling"""
            try:
                if state_dict is None:
                    state_dict = {"config": config}
                
                config_obj = state_dict.get("config", config)
                
                # Run ridership analytics agent
                ridership_results = ridership_analytics_agent(
                    ridership_csv=ridership_path,
                    question=question,
                    config=config_obj
                )
                
                # Store results in state
                state_dict["ridership_results"] = ridership_results
                
                # Create visualizations
                visualizations = visualization_agent(
                    ridership_results=ridership_results,
                    simulation_results={},
                    recommendation_results={},
                    config=config_obj
                )
                
                state_dict["visualizations"] = visualizations
                
                # Update UI
                recovery_chart = visualizations.get('recovery_chart', None)
                region_chart = visualizations.get('region_chart', None)
                
                return (
                    state_dict,
                    ridership_results['summary_txt'],
                    recovery_chart,
                    region_chart
                )
                
            except Exception as e:
                print(f"Error in analyze_ridership: {str(e)}")
                error_msg = f"Error analyzing ridership: {str(e)}"
                return state_dict, error_msg, None, None
        
        def simulate_scenario(scenario_text, elasticity_path, state_dict):
            """Handle scenario simulation button click with error handling"""
            try:
                if state_dict is None or not scenario_text.strip():
                    return state_dict, None, None, None
                
                config_obj = state_dict.get("config", config)
                ridership_results = state_dict.get("ridership_results", None)
                
                if not ridership_results:
                    error_msg = "Please run ridership analysis first."
                    return state_dict, pd.DataFrame([{"error": error_msg}]), None, None
                
                # Parse scenario text
                scenario_json = parse_scenario_text(scenario_text)
                
                if not scenario_json:
                    error_msg = "Could not parse scenario. Please try again."
                    return state_dict, pd.DataFrame([{"error": error_msg}]), None, None
                
                # Run simulation agent
                simulation_results = simulation_agent(
                    scenario_json=scenario_json,
                    recovery_df=ridership_results['recovery_df'],
                    elasticities_path=elasticity_path,
                    config=config_obj
                )
                
                # Store results in state
                state_dict["simulation_results"] = simulation_results
                
                # Create visualizations
                visualizations = visualization_agent(
                    ridership_results=ridership_results,
                    simulation_results=simulation_results,
                    recommendation_results={},
                    config=config_obj
                )
                
                state_dict["visualizations"] = visualizations
                
                # Update UI
                impact_df = simulation_results['impact_df']
                impact_chart = visualizations.get('impact_chart', None)
                radar_chart = visualizations.get('radar_chart', None)
                
                return state_dict, impact_df, impact_chart, radar_chart
                
            except Exception as e:
                print(f"Error in simulate_scenario: {str(e)}")
                error_msg = f"Error simulating scenario: {str(e)}"
                return state_dict, pd.DataFrame([{"error": error_msg}]), None, None
        
        def generate_recommendations(budget_cap, optimize, state_dict):
            """Handle recommendation button click with error handling"""
            try:
                if state_dict is None:
                    return state_dict, "Please run analysis first.", None
                
                config_obj = state_dict.get("config", config)
                simulation_results = state_dict.get("simulation_results", None)
                ridership_results = state_dict.get("ridership_results", None)
                
                if not simulation_results:
                    return state_dict, "Please run scenario simulation first.", None
                
                # Run recommendation agent
                recommendation_results = recommendation_agent(
                    impact_df=simulation_results['impact_df'],
                    budget_cap=budget_cap,
                    optimize=optimize,
                    config=config_obj
                )
                
                # Store results in state
                state_dict["recommendation_results"] = recommendation_results
                
                # Create visualizations
                visualizations = visualization_agent(
                    ridership_results=ridership_results or {},
                    simulation_results=simulation_results,
                    recommendation_results=recommendation_results,
                    config=config_obj
                )
                
                state_dict["visualizations"] = visualizations
                
                # Update UI
                top_scenarios_chart = visualizations.get('top_scenarios_chart', None)
                
                return state_dict, recommendation_results['exec_summary'], top_scenarios_chart
                
            except Exception as e:
                print(f"Error in generate_recommendations: {str(e)}")
                error_msg = f"Error generating recommendations: {str(e)}"
                return state_dict, error_msg, None
        
        # Connect event handlers with proper error handling
        analyze_btn.click(
            fn=analyze_ridership,
            inputs=[ridership_file, ridership_question, state],
            outputs=[state, ridership_summary, ridership_chart, region_chart],
            show_progress=True
        )
        
        simulate_btn.click(
            fn=simulate_scenario,
            inputs=[scenario_input, elasticity_file, state],
            outputs=[state, scenario_results, impact_chart, radar_chart],
            show_progress=True
        )
        
        recommend_btn.click(
            fn=generate_recommendations,
            inputs=[budget_cap, optimize_cb, state],
            outputs=[state, exec_summary, top_scenarios_chart],
            show_progress=True
        )
    
    return dashboard


def main():
    """Main application entry point"""
    print("🚀 Starting Transit Scenario Copilot...")
    
    # Create config
    config = Config()
    
    # Create output directory
    setup_output_dir(config)
    
    # Create and launch dashboard
    dashboard = create_dashboard(config)
    
    print("✅ Dashboard ready! Launching...")
    dashboard.launch(
        share=True,
        server_name="127.0.0.1",
        server_port=7860,
        show_error=True
    )


if __name__ == "__main__":
    main()
