#!/usr/bin/env python3
"""
Transit Scenario Copilot - APTA.com Conference Demo
==================================================

A multi-agent dashboard for transit planning and scenario analysis.

Agent Architecture:
    ┌─────────────────────┐     ┌──────────────────┐     ┌────────────────────┐     ┌────────────────┐
    │ RIDERSHIP ANALYTICS │────▶│ SIMULATION AGENT │────▶│ RECOMMENDATION     │────▶│ VISUALIZATION  │
    │      AGENT          │     │                  │     │     AGENT          │     │     AGENT      │
    └─────────────────────┘     └──────────────────┘     └────────────────────┘     └────────────────┘
           ▲                            ▲                           ▲                       ▲
           │                            │                           │                       │
      GTFS + CSV                 Elasticities +              Impact data +            Dashboard UI
                                   Scenario                  Budget constraints
"""

import os
import sys
import json
import time
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import warnings
warnings.filterwarnings('ignore')

# Core dependencies
import pandas as pd
import numpy as np
import gradio as gr
from dotenv import load_dotenv

# Visualization libraries
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots

# AutoGen for agent framework
try:
    import autogen
    AUTOGEN_AVAILABLE = True
except ImportError:
    AUTOGEN_AVAILABLE = False
    print("Warning: AutoGen not available. Using simplified agent structure.")

# Load environment variables from .env file
load_dotenv()

# LLM setup - we'll use OpenAI but mock if not available
try:
    import openai
    OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
    LLM_AVAILABLE = bool(OPENAI_API_KEY)
except ImportError:
    LLM_AVAILABLE = False

# === Configuration ===
@dataclass
class Config:
    """Central config for the Transit Scenario Copilot"""
    output_dir: Path = Path("./outputs")
    timestamp: str = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Default demo data paths
    default_ridership: str = "upt_prediction3.csv"
    default_elasticities: str = "elasticities.csv"
    
    # LLM settings
    llm_model: str = "gpt-4o"
    llm_temperature: float = 0.3
    
    # Simulation defaults
    default_elasticity_fare: float = -0.3  # 10% fare increase = 3% ridership decrease
    default_elasticity_frequency: float = 0.4  # 10% frequency increase = 4% ridership increase
    
    # APTA theme colors
    colors = {
        'primary': '#0055A4',  # APTA blue
        'secondary': '#D13239',  # APTA red
        'accent1': '#00A651',  # Green
        'accent2': '#F7941D',  # Orange
        'accent3': '#662D91',  # Purple
        'background': '#F5F7FA',
        'text': '#333333',
    }


# === Utility Functions ===
def setup_output_dir(config: Config) -> Path:
    """Create timestamped output directory"""
    output_path = config.output_dir / config.timestamp
    output_path.mkdir(parents=True, exist_ok=True)
    print(f"✓ Output directory: {output_path}")
    return output_path


def parse_scenario_text(scenario_text: str) -> List[Dict[str, Any]]:
    """
    Parse natural language scenario into structured format.
    Example: "increase rail Red Line frequency 20% off-peak ; cut weekend bus fares 25%"
    """
    # This is a simplified parser - in production, you'd use NLP or structured input
    scenarios = []
    
    # Split by semicolon for multiple changes
    changes = scenario_text.split(';')
    
    for change in changes:
        change = change.strip().lower()
        scenario = {}
        
        # Detect action (increase/decrease/cut)
        if 'increase' in change:
            scenario['action'] = 'increase'
        elif 'decrease' in change or 'cut' in change or 'reduce' in change:
            scenario['action'] = 'decrease'
        else:
            continue
        
        # Detect mode (rail/bus)
        if 'rail' in change:
            scenario['mode'] = 'rail'
        elif 'bus' in change:
            scenario['mode'] = 'bus'
        else:
            scenario['mode'] = 'all'
        
        # Detect what's changing (frequency/fare)
        if 'frequency' in change or 'headway' in change or 'service' in change:
            scenario['variable'] = 'frequency'
        elif 'fare' in change or 'price' in change or 'cost' in change:
            scenario['variable'] = 'fare'
        
        # Extract percentage (look for number followed by %)
        import re
        pct_match = re.search(r'(\d+)%', change)
        if pct_match:
            scenario['percent_change'] = float(pct_match.group(1)) / 100
            if scenario['action'] == 'decrease':
                scenario['percent_change'] *= -1
        
        # Extract route if specified
        if 'red line' in change:
            scenario['route'] = 'Red Line'
        elif 'blue line' in change:
            scenario['route'] = 'Blue Line'
        
        # Extract time period
        if 'off-peak' in change or 'offpeak' in change:
            scenario['time_period'] = 'off-peak'
        elif 'peak' in change:
            scenario['time_period'] = 'peak'
        elif 'weekend' in change:
            scenario['time_period'] = 'weekend'
        else:
            scenario['time_period'] = 'all'
        
        if 'variable' in scenario and 'percent_change' in scenario:
            scenarios.append(scenario)
    
    return scenarios


def mock_llm_response(prompt: str) -> str:
    """Fallback when no LLM is available"""
    if "recovery" in prompt.lower():
        return "Transit ridership shows strong recovery patterns, with rail at 78% and bus at 65% of pre-COVID levels."
    elif "recommend" in prompt.lower():
        return "The optimal scenario balances ridership gains with revenue sustainability. Focus on high-elasticity routes."
    else:
        return "Analysis complete. Key findings identified in the data."


def call_llm(prompt: str, config: Config) -> str:
    """Call LLM with fallback to mock"""
    if not LLM_AVAILABLE:
        return mock_llm_response(prompt)
    
    try:
        client = openai.OpenAI(api_key=OPENAI_API_KEY)
        response = client.chat.completions.create(
            model=config.llm_model,
            messages=[{"role": "user", "content": prompt}],
            temperature=config.llm_temperature
        )
        return response.choices[0].message.content
    except Exception as e:
        print(f"LLM call failed: {e}. Using mock response.")
        return mock_llm_response(prompt)


# === Agent 1: Ridership Analytics ===
def ridership_analytics_agent(
    ridership_csv: Optional[Path] = None,
    question: Optional[str] = None,
    config: Config = Config()
) -> Dict[str, Any]:
    """
    Agent 1: Analyze current ridership patterns and recovery rates.
    Returns recovery percentages by mode/route and answers NL questions.
    """
    print("\n🔍 RIDERSHIP ANALYTICS AGENT")
    
    # Load ridership data
    if ridership_csv and Path(ridership_csv).exists():
        df = pd.read_csv(ridership_csv)
        print(f"  → Loaded {len(df):,} ridership records")
    else:
        # Create mock data if file doesn't exist
        print("  → Creating mock ridership data")
        dates = pd.date_range('2024-01-01', periods=52, freq='W')
        modes = ['Rail', 'Bus']
        df = pd.DataFrame({
            'Week of': np.repeat(dates, len(modes)),
            'Mode': modes * len(dates),
            'Prediction': np.random.randint(50000, 200000, size=len(dates) * len(modes)),
            'Comparison Pre-Covid Prediction': np.random.randint(80000, 250000, size=len(dates) * len(modes))
        })
    
    # Extract mode information
    if 'Mode' not in df.columns:
        # Try to derive mode from Name column
        if 'Name' in df.columns:
            df['Mode'] = 'Bus'  # Default to bus
            rail_keywords = ['rail', 'metro', 'subway', 'train', 'transit']
            for keyword in rail_keywords:
                df.loc[df['Name'].str.lower().str.contains(keyword), 'Mode'] = 'Rail'
        else:
            # Create a mock Mode column
            df['Mode'] = np.random.choice(['Bus', 'Rail'], size=len(df))
    
    # Calculate recovery percentages
    df['recovery_pct'] = (df['Prediction'] / df['Comparison Pre-Covid Prediction']) * 100
    
    # Aggregate by mode
    recovery_by_mode = df.groupby('Mode')[['Prediction', 'Comparison Pre-Covid Prediction', 'recovery_pct']].mean()
    recovery_by_mode = recovery_by_mode.round(1)
    
    # Generate summary
    summary_parts = []
    for mode, row in recovery_by_mode.iterrows():
        summary_parts.append(
            f"{mode}: {row['recovery_pct']:.1f}% recovered "
            f"({row['Prediction']:,.0f} vs {row['Comparison Pre-Covid Prediction']:,.0f} pre-COVID)"
        )
    
    base_summary = "Ridership Recovery Status:\n" + "\n".join(summary_parts)
    
    # Handle natural language questions
    if question:
        prompt = f"""
        Transit ridership data summary:
        {base_summary}
        
        Question: {question}
        
        Provide a concise, data-driven answer.
        """
        nl_answer = call_llm(prompt, config)
        summary_txt = f"{base_summary}\n\nQ: {question}\nA: {nl_answer}"
    else:
        summary_txt = base_summary
    
    print(f"\n  ✓ Recovery analysis complete")
    
    # Additional regional analysis
    region_analysis = {}
    if 'Region' in df.columns:
        region_recovery = df.groupby(['Region', 'Mode'])[['Prediction', 'Comparison Pre-Covid Prediction', 'recovery_pct']].mean()
        region_analysis = {
            'region_recovery': region_recovery.round(1),
            'region_summary': f"Regional recovery rates calculated for {len(region_recovery)} region-mode combinations."
        }
    
    # City analysis
    city_analysis = {}
    if 'City' in df.columns:
        city_recovery = df.groupby(['City', 'Mode'])[['Prediction', 'Comparison Pre-Covid Prediction', 'recovery_pct']].mean()
        top_cities = city_recovery.nlargest(5, 'recovery_pct')
        bottom_cities = city_recovery.nsmallest(5, 'recovery_pct')
        
        city_analysis = {
            'city_recovery': city_recovery.round(1),
            'top_cities': top_cities,
            'bottom_cities': bottom_cities,
            'city_summary': f"City recovery analysis complete for {len(city_recovery)} city-mode combinations."
        }
    
    return {
        'recovery_df': recovery_by_mode,
        'summary_txt': summary_txt,
        'full_df': df,
        'region_analysis': region_analysis,
        'city_analysis': city_analysis
    }


# === Agent 2: Simulation Agent ===
def simulation_agent(
    scenario_json: List[Dict[str, Any]],
    recovery_df: pd.DataFrame,
    elasticities_path: Optional[Path] = None,
    config: Config = Config()
) -> Dict[str, Any]:
    """
    Agent 2: Apply elasticity-based simulation to scenarios.
    Calculate ridership & revenue impacts, vehicle-hour requirements.
    """
    print("\n⚡ SIMULATION AGENT")
    
    # Load or create elasticities
    if elasticities_path and Path(elasticities_path).exists():
        elasticities = pd.read_csv(elasticities_path)
    else:
        # Default elasticities
        elasticities = pd.DataFrame({
            'mode': ['rail', 'bus'],
            'fare_elasticity': [-0.3, -0.4],  # More elastic for bus
            'frequency_elasticity': [0.4, 0.5]  # More responsive for bus
        })
    
    # Prepare results
    impacts = []
    
    for scenario in scenario_json:
        print(f"  → Simulating: {scenario}")
        
        mode = scenario.get('mode', 'all')
        variable = scenario.get('variable', 'fare')
        pct_change = scenario.get('percent_change', 0)
        
        # Get appropriate elasticity
        if mode == 'all':
            modes_to_simulate = ['rail', 'bus']
        else:
            modes_to_simulate = [mode]
        
        for sim_mode in modes_to_simulate:
            # Get elasticity value
            mode_elasticity = elasticities[elasticities['mode'] == sim_mode]
            if mode_elasticity.empty:
                elasticity_value = config.default_elasticity_fare if variable == 'fare' else config.default_elasticity_frequency
            else:
                elasticity_value = mode_elasticity[f'{variable}_elasticity'].iloc[0]
            
            # Calculate ridership impact
            # Elasticity: % change in ridership / % change in variable
            ridership_pct_change = elasticity_value * pct_change * 100
            
            # Get baseline from recovery_df
            sim_mode_title = sim_mode.title()
            base_ridership = recovery_df.loc[sim_mode_title, 'Prediction'] if sim_mode_title in recovery_df.index else 100000
            new_ridership = base_ridership * (1 + ridership_pct_change / 100)
            
            # Revenue calculations (simplified)
            if variable == 'fare':
                # Revenue changes with both fare and ridership
                revenue_pct_change = (1 + pct_change) * (1 + ridership_pct_change / 100) - 1
            else:
                # Frequency changes don't directly affect fare revenue
                revenue_pct_change = ridership_pct_change / 100
            
            # Vehicle-hour requirements (for frequency changes)
            if variable == 'frequency':
                vehicle_hours_change = pct_change  # Direct relationship
            else:
                vehicle_hours_change = 0
            
            # Crowding proxy (simplified)
            crowding_index = new_ridership / (base_ridership * (1 + vehicle_hours_change))
            
            impacts.append({
                'scenario': f"{scenario['action']} {sim_mode} {variable} {abs(pct_change)*100:.0f}%",
                'mode': sim_mode,
                'variable': variable,
                'pct_change': pct_change,
                'ridership_impact_pct': ridership_pct_change,
                'revenue_impact_pct': revenue_pct_change * 100,
                'vehicle_hours_change_pct': vehicle_hours_change * 100,
                'crowding_index': crowding_index,
                'new_ridership': new_ridership,
                'base_ridership': base_ridership
            })
    
    impact_df = pd.DataFrame(impacts)
    
    print(f"\n  ✓ Simulated {len(impacts)} scenarios")
    
    return {
        'impact_df': impact_df,
        'elasticities_used': elasticities
    }


# === Agent 3: Recommendation Agent ===
def recommendation_agent(
    impact_df: pd.DataFrame,
    budget_cap: Optional[float] = None,
    optimize: bool = False,
    config: Config = Config()
) -> Dict[str, Any]:
    """
    Agent 3: Rank scenarios and generate executive recommendations.
    Can brute-force search if optimize=True.
    """
    print("\n💡 RECOMMENDATION AGENT")
    
    if impact_df.empty:
        return {
            'top_n_df': pd.DataFrame(),
            'exec_summary': "No scenarios to evaluate."
        }
    
    # Calculate composite score
    # Prioritize: ridership gain, revenue sustainability, minimal vehicle hours
    impact_df['composite_score'] = (
        impact_df['ridership_impact_pct'] * 0.5 +  # 50% weight on ridership
        impact_df['revenue_impact_pct'] * 0.3 +     # 30% weight on revenue
        -impact_df['vehicle_hours_change_pct'] * 0.2  # 20% weight on efficiency
    )
    
    # Apply budget constraints if provided
    if budget_cap:
        # Simplified: assume each 1% vehicle hour increase = $100k annual cost
        impact_df['estimated_cost_m'] = impact_df['vehicle_hours_change_pct'] * 0.1
        impact_df = impact_df[impact_df['estimated_cost_m'] <= budget_cap]
    
    # Sort by composite score
    top_scenarios = impact_df.nlargest(3, 'composite_score')[
        ['scenario', 'ridership_impact_pct', 'revenue_impact_pct', 'composite_score']
    ].round(1)
    
    # Generate executive summary
    if len(top_scenarios) > 0:
        top_scenario = top_scenarios.iloc[0]
        
        prompt = f"""
        Generate a 3-sentence executive summary for this transit scenario recommendation:
        
        Top scenario: {top_scenario['scenario']}
        Ridership impact: {top_scenario['ridership_impact_pct']:.1f}%
        Revenue impact: {top_scenario['revenue_impact_pct']:.1f}%
        
        Make it concise, action-oriented, and highlight the key benefit.
        """
        
        exec_summary = call_llm(prompt, config)
    else:
        exec_summary = "No viable scenarios found within constraints."
    
    # Optimize mode (grid search)
    if optimize and len(impact_df) > 1:
        print("  → Running optimization grid search...")
        # This is where you'd implement more sophisticated optimization
        # For demo, we'll just note it as a TODO
        exec_summary += "\n\n[Optimization mode: Advanced grid search available with --optimize flag]"
    
    print(f"\n  ✓ Generated recommendations")
    
    return {
        'top_n_df': top_scenarios,
        'exec_summary': exec_summary,
        'full_impact_df': impact_df
    }


# === Agent 4: Visualization Agent ===
def visualization_agent(
    ridership_results: Dict[str, Any],
    simulation_results: Dict[str, Any],
    recommendation_results: Dict[str, Any],
    config: Config = Config()
) -> Dict[str, Any]:
    """
    Agent 4: Create visualizations for dashboard display.
    Generates charts for ridership recovery, scenario impacts, and recommendations.
    """
    print("\n📊 VISUALIZATION AGENT")
    
    visualizations = {}
    
    # 1. Ridership Recovery Visualization
    if 'recovery_df' in ridership_results and not ridership_results['recovery_df'].empty:
        recovery_df = ridership_results['recovery_df']
        
        # Create a bar chart for recovery percentages
        fig_recovery = go.Figure()
        
        for mode in recovery_df.index:
            fig_recovery.add_trace(go.Bar(
                x=[mode],
                y=[recovery_df.loc[mode, 'recovery_pct']],
                name=mode,
                marker_color=config.colors['primary'] if mode == 'Rail' else config.colors['secondary'],
                text=[f"{recovery_df.loc[mode, 'recovery_pct']:.1f}%"],
                textposition='auto'
            ))
        
        fig_recovery.update_layout(
            title='Ridership Recovery by Mode',
            xaxis_title='Mode',
            yaxis_title='Recovery Percentage (%)',
            yaxis=dict(range=[0, 110]),
            plot_bgcolor=config.colors['background'],
            font=dict(color=config.colors['text']),
            height=400
        )
        
        visualizations['recovery_chart'] = fig_recovery
        
        # Regional analysis if available
        if 'region_analysis' in ridership_results and 'region_recovery' in ridership_results['region_analysis']:
            region_recovery = ridership_results['region_analysis']['region_recovery']
            
            # Pivot the data for better visualization
            region_pivot = region_recovery.reset_index().pivot(index='Region', columns='Mode', values='recovery_pct')
            
            # Create a grouped bar chart for regional recovery
            fig_region = px.bar(
                region_pivot.reset_index().melt(id_vars='Region', var_name='Mode', value_name='Recovery (%)'),
                x='Region',
                y='Recovery (%)',
                color='Mode',
                barmode='group',
                color_discrete_map={'Rail': config.colors['primary'], 'Bus': config.colors['secondary']},
                title='Ridership Recovery by Region and Mode'
            )
            
            fig_region.update_layout(
                plot_bgcolor=config.colors['background'],
                font=dict(color=config.colors['text']),
                height=400
            )
            
            visualizations['region_chart'] = fig_region
    
    # 2. Simulation Impact Visualization
    if 'impact_df' in simulation_results and not simulation_results['impact_df'].empty:
        impact_df = simulation_results['impact_df']
        
        # Create a bar chart for ridership impact
        fig_impact = go.Figure()
        
        # Color mapping based on mode and variable
        color_map = {
            'rail_fare': config.colors['primary'],
            'rail_frequency': config.colors['accent1'],
            'bus_fare': config.colors['secondary'],
            'bus_frequency': config.colors['accent2']
        }
        
        for _, row in impact_df.iterrows():
            color_key = f"{row['mode']}_{row['variable']}"
            color = color_map.get(color_key, config.colors['accent3'])
            
            fig_impact.add_trace(go.Bar(
                x=[row['scenario']],
                y=[row['ridership_impact_pct']],
                name=row['scenario'],
                marker_color=color,
                text=[f"{row['ridership_impact_pct']:.1f}%"],
                textposition='auto'
            ))
        
        fig_impact.update_layout(
            title='Ridership Impact by Scenario',
            xaxis_title='Scenario',
            yaxis_title='Ridership Change (%)',
            plot_bgcolor=config.colors['background'],
            font=dict(color=config.colors['text']),
            height=400
        )
        
        visualizations['impact_chart'] = fig_impact
        
        # Create a radar chart for multi-dimensional comparison
        if len(impact_df) > 0:
            # Prepare data for radar chart
            categories = ['Ridership Impact', 'Revenue Impact', 'Vehicle Hours', 'Crowding Index']
            
            fig_radar = go.Figure()
            
            for _, row in impact_df.iterrows():
                color_key = f"{row['mode']}_{row['variable']}"
                color = color_map.get(color_key, config.colors['accent3'])
                
                # Normalize values for radar chart
                ridership_impact = row['ridership_impact_pct']
                revenue_impact = row['revenue_impact_pct']
                vehicle_hours = -row['vehicle_hours_change_pct']  # Invert so less is better
                crowding = (1 - row['crowding_index']) * 100  # Invert so less crowding is better
                
                fig_radar.add_trace(go.Scatterpolar(
                    r=[ridership_impact, revenue_impact, vehicle_hours, crowding],
                    theta=categories,
                    fill='toself',
                    name=row['scenario'],
                    line_color=color
                ))
            
            fig_radar.update_layout(
                polar=dict(
                    radialaxis=dict(
                        visible=True,
                        range=[-20, 20]
                    )
                ),
                title='Multi-dimensional Scenario Comparison',
                plot_bgcolor=config.colors['background'],
                font=dict(color=config.colors['text']),
                height=400
            )
            
            visualizations['radar_chart'] = fig_radar
    
    # 3. Recommendation Visualization
    if 'top_n_df' in recommendation_results and not recommendation_results['top_n_df'].empty:
        top_scenarios = recommendation_results['top_n_df']
        
        # Create a horizontal bar chart for top scenarios
        fig_top = go.Figure()
        
        for i, (_, row) in enumerate(top_scenarios.iterrows()):
            fig_top.add_trace(go.Bar(
                y=[row['scenario']],
                x=[row['composite_score']],
                orientation='h',
                name=row['scenario'],
                marker_color=[config.colors['primary'], config.colors['accent1'], config.colors['accent2']][i % 3],
                text=[f"Score: {row['composite_score']:.1f}"],
                textposition='auto'
            ))
        
        fig_top.update_layout(
            title='Top Recommended Scenarios',
            xaxis_title='Composite Score',
            yaxis_title='Scenario',
            plot_bgcolor=config.colors['background'],
            font=dict(color=config.colors['text']),
            height=400
        )
        
        visualizations['top_scenarios_chart'] = fig_top
    
    print(f"\n  ✓ Generated {len(visualizations)} visualizations")
    
    return visualizations


# === AutoGen Agent Setup ===
def setup_autogen_agents(config: Config):
    """Set up AutoGen agents for collaborative scenario planning"""
    if not AUTOGEN_AVAILABLE:
        return None
    
    # Configure LLM
    llm_config = {
        "config_list": [{"model": config.llm_model, "api_key": OPENAI_API_KEY}],
        "temperature": config.llm_temperature
    }
    
    # Create agents
    ridership_agent = autogen.AssistantAgent(
        name="RidershipAnalyticsAgent",
        system_message="You analyze transit ridership data and recovery patterns. Focus on data-driven insights.",
        llm_config=llm_config
    )
    
    simulation_agent = autogen.AssistantAgent(
        name="SimulationAgent",
        system_message="You simulate transit scenarios using elasticity models. Evaluate impacts on ridership, revenue, and operations.",
        llm_config=llm_config
    )
    
    recommendation_agent = autogen.AssistantAgent(
        name="RecommendationAgent",
        system_message="You rank transit scenarios and provide executive recommendations. Focus on actionable insights.",
        llm_config=llm_config
    )
    
    visualization_agent = autogen.AssistantAgent(
        name="VisualizationAgent",
        system_message="You create visualizations for transit data and scenario analysis. Focus on clarity and insight.",
        llm_config=llm_config
    )
    
    user_proxy = autogen.UserProxyAgent(
        name="TransitPlanner",
        human_input_mode="NEVER",
        max_consecutive_auto_reply=10,
        is_termination_msg=lambda x: "TERMINATE" in x.get("content", ""),
        code_execution_config={"work_dir": "autogen_output"}
    )
    
    return {
        "ridership_agent": ridership_agent,
        "simulation_agent": simulation_agent,
        "recommendation_agent": recommendation_agent,
        "visualization_agent": visualization_agent,
        "user_proxy": user_proxy
    }


# === Gradio Dashboard Interface ===
def create_dashboard(config: Config):
    """Create Gradio dashboard interface"""
    
    # Custom CSS for APTA theme
    css = f"""
    .gradio-container {{
        background-color: {config.colors['background']};
    }}
    .tabs {{
        border-color: {config.colors['primary']};
    }}
    .tab-selected {{
        background-color: {config.colors['primary']};
        color: white;
    }}
    h1, h2, h3 {{
        color: {config.colors['primary']};
    }}
    .primary-button {{
        background-color: {config.colors['primary']} !important;
    }}
    .secondary-button {{
        background-color: {config.colors['secondary']} !important;
    }}
    """
    
    # Dashboard title and description
    title = "Transit Scenario Copilot"
    description = """
    <div style="text-align: center; margin-bottom: 20px;">
        <h1 style="color: #0055A4;">Transit Scenario Copilot</h1>
        <h3 style="color: #D13239;">APTA.com Conference Demo</h3>
        <p>A multi-agent AI system for transit planning and scenario analysis</p>
    </div>
    """
    
    # Initialize state variables
    state = gr.State({
        "ridership_results": None,
        "simulation_results": None,
        "recommendation_results": None,
        "visualizations": None,
        "config": config
    })
    
    # Define dashboard layout
    with gr.Blocks(css=css) as dashboard:
        gr.HTML(description)
        
        # Main tabs
        with gr.Tabs() as tabs:
            # Tab 1: Data Input & Ridership Analysis
            with gr.Tab("1️⃣ Ridership Analysis", id=1):
                with gr.Row():
                    with gr.Column(scale=1):
                        ridership_file = gr.File(
                            label="Upload Ridership Data (CSV)",
                            file_types=[".csv"],
                            value=config.default_ridership
                        )
                        ridership_question = gr.Textbox(
                            label="Ask a question about ridership (optional)",
                            placeholder="e.g., Which regions show the strongest recovery?"
                        )
                        analyze_btn = gr.Button("Analyze Ridership", variant="primary")
                    
                    with gr.Column(scale=2):
                        ridership_summary = gr.Textbox(
                            label="Ridership Analysis Summary",
                            placeholder="Analysis results will appear here...",
                            lines=10,
                            interactive=False
                        )
                
                with gr.Row():
                    ridership_chart = gr.Plot(label="Ridership Recovery Chart")
                    region_chart = gr.Plot(label="Regional Recovery Chart")
            
            # Tab 2: Scenario Simulation
            with gr.Tab("2️⃣ Scenario Simulation", id=2):
                with gr.Row():
                    with gr.Column(scale=1):
                        scenario_input = gr.Textbox(
                            label="Enter Scenario (Natural Language)",
                            placeholder="e.g., increase rail frequency 20% off-peak; cut weekend bus fares 25%",
                            lines=3
                        )
                        elasticity_file = gr.File(
                            label="Upload Elasticities (CSV, optional)",
                            file_types=[".csv"]
                        )
                        simulate_btn = gr.Button("Simulate Scenario", variant="primary")
                    
                    with gr.Column(scale=2):
                        scenario_results = gr.Dataframe(
                            label="Scenario Simulation Results",
                            headers=["scenario", "mode", "variable", "ridership_impact_pct", "revenue_impact_pct"],
                            interactive=False
                        )
                
                with gr.Row():
                    impact_chart = gr.Plot(label="Ridership Impact Chart")
                    radar_chart = gr.Plot(label="Multi-dimensional Comparison")
            
            # Tab 3: Recommendations
            with gr.Tab("3️⃣ Recommendations", id=3):
                with gr.Row():
                    with gr.Column(scale=1):
                        budget_cap = gr.Number(
                            label="Budget Cap ($ millions, optional)",
                            value=None
                        )
                        optimize_cb = gr.Checkbox(
                            label="Run Optimization",
                            value=False
                        )
                        recommend_btn = gr.Button("Generate Recommendations", variant="primary")
                    
                    with gr.Column(scale=2):
                        exec_summary = gr.Textbox(
                            label="Executive Summary",
                            placeholder="Recommendations will appear here...",
                            lines=6,
                            interactive=False
                        )
                
                with gr.Row():
                    top_scenarios_chart = gr.Plot(label="Top Recommended Scenarios")
            
            # Tab 4: Agent Collaboration
            with gr.Tab("4️⃣ Agent Collaboration", id=4):
                with gr.Row():
                    with gr.Column():
                        agent_query = gr.Textbox(
                            label="Ask the Agent Team",
                            placeholder="e.g., What strategies would increase ridership while maintaining revenue?",
                            lines=3
                        )
                        agent_btn = gr.Button("Submit to Agents", variant="primary")
                
                with gr.Row():
                    agent_response = gr.Textbox(
                        label="Agent Team Response",
                        placeholder="Agent collaboration results will appear here...",
                        lines=10,
                        interactive=False
                    )
                
                with gr.Row():
                    agent_chart = gr.Plot(label="Agent Analysis Visualization")
        
        # Define event handlers
        def analyze_ridership(ridership_path, question, state_dict):
            """Handle ridership analysis button click"""
            config = state_dict["config"]
            
            # Run ridership analytics agent
            ridership_results = ridership_analytics_agent(
                ridership_csv=ridership_path,
                question=question,
                config=config
            )
            
            # Store results in state
            state_dict["ridership_results"] = ridership_results
            
            # Create visualizations
            visualizations = visualization_agent(
                ridership_results=ridership_results,
                simulation_results={},
                recommendation_results={},
                config=config
            )
            
            state_dict["visualizations"] = visualizations
            
            # Update UI
            recovery_chart = visualizations.get('recovery_chart', None)
            region_chart = visualizations.get('region_chart', None)
            
            return state_dict, ridership_results['summary_txt'], recovery_chart, region_chart
        
        def simulate_scenario(scenario_text, elasticity_path, state_dict):
            """Handle scenario simulation button click"""
            config = state_dict["config"]
            ridership_results = state_dict.get("ridership_results", None)
            
            if not ridership_results:
                return state_dict, gr.Dataframe(value=None), None, None, "Please run ridership analysis first."
            
            # Parse scenario text
            scenario_json = parse_scenario_text(scenario_text)
            
            if not scenario_json:
                return state_dict, gr.Dataframe(value=None), None, None, "Could not parse scenario. Please try again."
            
            # Run simulation agent
            simulation_results = simulation_agent(
                scenario_json=scenario_json,
                recovery_df=ridership_results['recovery_df'],
                elasticities_path=elasticity_path,
                config=config
            )
            
            # Store results in state
            state_dict["simulation_results"] = simulation_results
            
            # Create visualizations
            visualizations = visualization_agent(
                ridership_results=ridership_results,
                simulation_results=simulation_results,
                recommendation_results={},
                config=config
            )
            
            state_dict["visualizations"] = visualizations
            
            # Update UI
            impact_df = simulation_results['impact_df']
            impact_chart = visualizations.get('impact_chart', None)
            radar_chart = visualizations.get('radar_chart', None)
            
            return state_dict, impact_df, impact_chart, radar_chart, ""
        
        def generate_recommendations(budget_cap, optimize, state_dict):
            """Handle recommendation button click"""
            config = state_dict["config"]
            simulation_results = state_dict.get("simulation_results", None)
            ridership_results = state_dict.get("ridership_results", None)
            
            if not simulation_results:
                return state_dict, "Please run scenario simulation first.", None
            
            # Run recommendation agent
            recommendation_results = recommendation_agent(
                impact_df=simulation_results['impact_df'],
                budget_cap=budget_cap,
                optimize=optimize,
                config=config
            )
            
            # Store results in state
            state_dict["recommendation_results"] = recommendation_results
            
            # Create visualizations
            visualizations = visualization_agent(
                ridership_results=ridership_results,
                simulation_results=simulation_results,
                recommendation_results=recommendation_results,
                config=config
            )
            
            state_dict["visualizations"] = visualizations
            
            # Update UI
            top_scenarios_chart = visualizations.get('top_scenarios_chart', None)
            
            return state_dict, recommendation_results['exec_summary'], top_scenarios_chart
        
        def run_agent_collaboration(query, state_dict):
            """Handle agent collaboration button click"""
            config = state_dict["config"]
            
            # Check if we have AutoGen available
            if not AUTOGEN_AVAILABLE:
                response = "Agent collaboration requires the AutoGen library. Using simplified response instead.\n\n"
                
                # Generate a response using the LLM directly
                prompt = f"""
                You are a transit planning expert. Answer the following question about transit planning:
                
                {query}
                
                Provide a concise, informative response.
                """
                
                llm_response = call_llm(prompt, config)
                response += llm_response
                
                return response, None
            
            # Set up AutoGen agents
            agents = setup_autogen_agents(config)
            
            if not agents:
                return "Agent collaboration is not available.", None
            
            # Prepare the conversation
            user_proxy = agents["user_proxy"]
            ridership_agent = agents["ridership_agent"]
            
            # Start the conversation
            user_proxy.initiate_chat(
                ridership_agent,
                message=f"Transit planning question: {query}"
            )
            
            # Get the conversation history
            chat_history = user_proxy.chat_messages[ridership_agent]
            
            # Extract the response
            response = "Agent Collaboration Results:\n\n"
            for message in chat_history[-3:]:  # Last 3 messages
                if message["role"] != "user":
                    response += f"{message['role'].capitalize()}: {message['content']}\n\n"
            
            # Create a simple visualization
            fig = go.Figure()
            
            fig.add_trace(go.Indicator(
                mode="gauge+number",
                value=75,
                title={'text': "Confidence Score"},
                gauge={'axis': {'range': [0, 100]},
                       'bar': {'color': config.colors['primary']},
                       'steps': [
                           {'range': [0, 50], 'color': config.colors['secondary']},
                           {'range': [50, 100], 'color': config.colors['accent1']}
                       ]}
            ))
            
            fig.update_layout(
                title="Agent Confidence Assessment",
                plot_bgcolor=config.colors['background'],
                font=dict(color=config.colors['text']),
                height=300
            )
            
            return response, fig
        
        # Connect event handlers
        analyze_btn.click(
            analyze_ridership,
            inputs=[ridership_file, ridership_question, state],
            outputs=[state, ridership_summary, ridership_chart, region_chart]
        )
        
        simulate_btn.click(
            simulate_scenario,
            inputs=[scenario_input, elasticity_file, state],
            outputs=[state, scenario_results, impact_chart, radar_chart, ridership_summary]
        )
        
        recommend_btn.click(
            generate_recommendations,
            inputs=[budget_cap, optimize_cb, state],
            outputs=[state, exec_summary, top_scenarios_chart]
        )
        
        agent_btn.click(
            run_agent_collaboration,
            inputs=[agent_query, state],
            outputs=[agent_response, agent_chart]
        )
    
    return dashboard


# === Main Application ===
def main():
    """Main application entry point"""
    # Create config
    config = Config()
    
    # Create output directory
    setup_output_dir(config)
    
    # Create and launch dashboard
    dashboard = create_dashboard(config)
    dashboard.launch(share=True)


if __name__ == "__main__":
    main()
