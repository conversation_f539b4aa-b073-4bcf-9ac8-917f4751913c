﻿#!/usr/bin/env python3
"""
Transit Scenario Copilot - A demo of AI agents for rapid transit planning
========================================================================

    ┌─────────────────────┐     ┌──────────────────┐     ┌────────────────────┐
    │ RIDERSHIP ANALYTICS │────▶│ SIMULATION AGENT │────▶│ RECOMMENDATION     │
    │      AGENT          │     │                  │     │     AGENT          │
    └─────────────────────┘     └──────────────────┘     └────────────────────┘
           ▲                            ▲                           ▲
           │                            │                           │
      GTFS + CSV                 Elasticities +              Impact data +
                                   Scenario                  Budget constraints

Run quickstart() for instant demo, or use CLI for custom scenarios.
"""

import os
import sys
import json
import argparse
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import warnings
warnings.filterwarnings('ignore')

# Core dependencies
import pandas as pd
import numpy as np

# Optional but nice-to-have imports
try:
    from rich.console import Console
    from rich.progress import track
    console = Console()
    log = console.print
except ImportError:
    log = print
    track = lambda x, description: x  # No-op if rich isn't available

try:
    import matplotlib.pyplot as plt
    import seaborn as sns
    sns.set_theme(style="whitegrid")
    PLOTTING_AVAILABLE = True
except ImportError:
    PLOTTING_AVAILABLE = False

# LLM setup - we'll use OpenAI but mock if not available
try:
    import openai
    LLM_AVAILABLE = bool(os.getenv('OPENAI_API_KEY'))
except ImportError:
    LLM_AVAILABLE = False

# GTFS parsing
try:
    import gtfs_kit as gk
    GTFS_AVAILABLE = True
except ImportError:
    GTFS_AVAILABLE = False
    log("[yellow]Warning: gtfs_kit not installed. GTFS parsing will be limited.[/yellow]")


# === Configuration ===
@dataclass
class Config:
    """Central config for the demo app"""
    output_dir: Path = Path("./outputs")
    timestamp: str = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Default demo data paths
    default_gtfs: str = "demo_gtfs.zip"
    default_ridership: str = "upt_prediction.csv"
    default_elasticities: str = "elasticities.csv"
    
    # LLM settings
    llm_model: str = "gpt-4o-mini"
    llm_temperature: float = 0.3
    
    # Simulation defaults
    default_elasticity_fare: float = -0.3  # 10% fare increase = 3% ridership decrease
    default_elasticity_frequency: float = 0.4  # 10% frequency increase = 4% ridership increase


# === Utility Functions ===
def setup_output_dir(config: Config) -> Path:
    """Create timestamped output directory"""
    output_path = config.output_dir / config.timestamp
    output_path.mkdir(parents=True, exist_ok=True)
    log(f"[green]✓ Output directory: {output_path}[/green]")
    return output_path


def parse_scenario_text(scenario_text: str) -> List[Dict[str, Any]]:
    """
    Parse natural language scenario into structured format.
    Example: "increase rail Red Line frequency 20% off-peak ; cut weekend bus fares 25%"
    """
    # This is a simplified parser - in production, you'd use NLP or structured input
    scenarios = []
    
    # Split by semicolon for multiple changes
    changes = scenario_text.split(';')
    
    for change in changes:
        change = change.strip().lower()
        scenario = {}
        
        # Detect action (increase/decrease/cut)
        if 'increase' in change:
            scenario['action'] = 'increase'
        elif 'decrease' in change or 'cut' in change:
            scenario['action'] = 'decrease'
        else:
            continue
        
        # Detect mode (rail/bus)
        if 'rail' in change:
            scenario['mode'] = 'rail'
        elif 'bus' in change:
            scenario['mode'] = 'bus'
        else:
            scenario['mode'] = 'all'
        
        # Detect what's changing (frequency/fare)
        if 'frequency' in change or 'headway' in change:
            scenario['variable'] = 'frequency'
        elif 'fare' in change or 'price' in change:
            scenario['variable'] = 'fare'
        
        # Extract percentage (look for number followed by %)
        import re
        pct_match = re.search(r'(\d+)%', change)
        if pct_match:
            scenario['percent_change'] = float(pct_match.group(1)) / 100
            if scenario['action'] == 'decrease':
                scenario['percent_change'] *= -1
        
        # Extract route if specified
        if 'red line' in change:
            scenario['route'] = 'Red Line'
        elif 'blue line' in change:
            scenario['route'] = 'Blue Line'
        
        # Extract time period
        if 'off-peak' in change or 'offpeak' in change:
            scenario['time_period'] = 'off-peak'
        elif 'peak' in change:
            scenario['time_period'] = 'peak'
        elif 'weekend' in change:
            scenario['time_period'] = 'weekend'
        else:
            scenario['time_period'] = 'all'
        
        if 'variable' in scenario and 'percent_change' in scenario:
            scenarios.append(scenario)
    
    return scenarios


def mock_llm_response(prompt: str) -> str:
    """Fallback when no LLM is available"""
    if "recovery" in prompt.lower():
        return "Transit ridership shows strong recovery patterns, with rail at 78% and bus at 65% of pre-COVID levels."
    elif "recommend" in prompt.lower():
        return "The optimal scenario balances ridership gains with revenue sustainability. Focus on high-elasticity routes."
    else:
        return "Analysis complete. Key findings identified in the data."


def call_llm(prompt: str, config: Config) -> str:
    """Call LLM with fallback to mock"""
    if not LLM_AVAILABLE:
        return mock_llm_response(prompt)
    
    try:
        client = openai.OpenAI()
        response = client.chat.completions.create(
            model=config.llm_model,
            messages=[{"role": "user", "content": prompt}],
            temperature=config.llm_temperature
        )
        return response.choices[0].message.content
    except Exception as e:
        log(f"[yellow]LLM call failed: {e}. Using mock response.[/yellow]")
        return mock_llm_response(prompt)


# === Agent 1: Ridership Analytics ===
def ridership_analytics_agent(
    gtfs_path: Optional[Path] = None,
    ridership_csv: Optional[Path] = None,
    question: Optional[str] = None,
    config: Config = Config()
) -> Dict[str, Any]:
    """
    Agent 1: Analyze current ridership patterns and recovery rates.
    Returns recovery percentages by mode/route and answers NL questions.
    """
    log("\n[bold blue]🔍 RIDERSHIP ANALYTICS AGENT[/bold blue]")
    
    # Load ridership data
    if ridership_csv and ridership_csv.exists():
        df = pd.read_csv(ridership_csv)
        log(f"  → Loaded {len(df):,} ridership records")
    else:
        # Create mock data if file doesn't exist
        log("  → Creating mock ridership data")
        dates = pd.date_range('2024-01-01', periods=52, freq='W')
        modes = ['Rail', 'Bus']
        df = pd.DataFrame({
            'Week of': np.repeat(dates, len(modes)),
            'Mode': modes * len(dates),
            'Prediction': np.random.randint(50000, 200000, size=len(dates) * len(modes)),
            'Comparison Pre-Covid Prediction': np.random.randint(80000, 250000, size=len(dates) * len(modes))
        })
    
    # Calculate recovery percentages
    df['recovery_pct'] = (df['Prediction'] / df['Comparison Pre-Covid Prediction']) * 100
    
    # Aggregate by mode
    recovery_by_mode = df.groupby('Mode')[['Prediction', 'Comparison Pre-Covid Prediction', 'recovery_pct']].mean()
    recovery_by_mode = recovery_by_mode.round(1)
    
    # Generate summary
    summary_parts = []
    for mode, row in recovery_by_mode.iterrows():
        summary_parts.append(
            f"{mode}: {row['recovery_pct']:.1f}% recovered "
            f"({row['Prediction']:,.0f} vs {row['Comparison Pre-Covid Prediction']:,.0f} pre-COVID)"
        )
    
    base_summary = "Ridership Recovery Status:\n" + "\n".join(summary_parts)
    
    # Handle natural language questions
    if question:
        prompt = f"""
        Transit ridership data summary:
        {base_summary}
        
        Question: {question}
        
        Provide a concise, data-driven answer.
        """
        nl_answer = call_llm(prompt, config)
        summary_txt = f"{base_summary}\n\nQ: {question}\nA: {nl_answer}"
    else:
        summary_txt = base_summary
    
    log(f"\n[green]  ✓ Recovery analysis complete[/green]")
    
    return {
        'recovery_df': recovery_by_mode,
        'summary_txt': summary_txt,
        'full_df': df
    }


# === Agent 2: Simulation Agent ===
def simulation_agent(
    scenario_json: List[Dict[str, Any]],
    recovery_df: pd.DataFrame,
    elasticities_path: Optional[Path] = None,
    config: Config = Config()
) -> Dict[str, Any]:
    """
    Agent 2: Apply elasticity-based simulation to scenarios.
    Calculate ridership & revenue impacts, vehicle-hour requirements.
    """
    log("\n[bold yellow]⚡ SIMULATION AGENT[/bold yellow]")
    
    # Load or create elasticities
    if elasticities_path and elasticities_path.exists():
        elasticities = pd.read_csv(elasticities_path)
    else:
        # Default elasticities
        elasticities = pd.DataFrame({
            'mode': ['rail', 'bus'],
            'fare_elasticity': [-0.3, -0.4],  # More elastic for bus
            'frequency_elasticity': [0.4, 0.5]  # More responsive for bus
        })
    
    # Prepare results
    impacts = []
    
    for scenario in scenario_json:
        log(f"  → Simulating: {scenario}")
        
        mode = scenario.get('mode', 'all')
        variable = scenario.get('variable', 'fare')
        pct_change = scenario.get('percent_change', 0)
        
        # Get appropriate elasticity
        if mode == 'all':
            modes_to_simulate = ['rail', 'bus']
        else:
            modes_to_simulate = [mode]
        
        for sim_mode in modes_to_simulate:
            # Get elasticity value
            mode_elasticity = elasticities[elasticities['mode'] == sim_mode]
            if mode_elasticity.empty:
                elasticity_value = config.default_elasticity_fare if variable == 'fare' else config.default_elasticity_frequency
            else:
                elasticity_value = mode_elasticity[f'{variable}_elasticity'].iloc[0]
            
            # Calculate ridership impact
            # Elasticity: % change in ridership / % change in variable
            ridership_pct_change = elasticity_value * pct_change * 100
            
            # Get baseline from recovery_df
            base_ridership = recovery_df.loc[sim_mode.title(), 'Prediction'] if sim_mode.title() in recovery_df.index else 100000
            new_ridership = base_ridership * (1 + ridership_pct_change / 100)
            
            # Revenue calculations (simplified)
            if variable == 'fare':
                # Revenue changes with both fare and ridership
                revenue_pct_change = (1 + pct_change) * (1 + ridership_pct_change / 100) - 1
            else:
                # Frequency changes don't directly affect fare revenue
                revenue_pct_change = ridership_pct_change / 100
            
            # Vehicle-hour requirements (for frequency changes)
            if variable == 'frequency':
                vehicle_hours_change = pct_change  # Direct relationship
            else:
                vehicle_hours_change = 0
            
            # Crowding proxy (simplified)
            crowding_index = new_ridership / (base_ridership * (1 + vehicle_hours_change))
            
            impacts.append({
                'scenario': f"{scenario['action']} {sim_mode} {variable} {abs(pct_change)*100:.0f}%",
                'mode': sim_mode,
                'variable': variable,
                'pct_change': pct_change,
                'ridership_impact_pct': ridership_pct_change,
                'revenue_impact_pct': revenue_pct_change * 100,
                'vehicle_hours_change_pct': vehicle_hours_change * 100,
                'crowding_index': crowding_index,
                'new_ridership': new_ridership,
                'base_ridership': base_ridership
            })
    
    impact_df = pd.DataFrame(impacts)
    
    # Create visualizations if available
    charts = []
    if PLOTTING_AVAILABLE and not impact_df.empty:
        # Chart 1: Ridership impact by scenario
        fig, ax = plt.subplots(figsize=(10, 6))
        impact_df.plot(x='scenario', y='ridership_impact_pct', kind='bar', ax=ax)
        ax.set_title('Ridership Impact by Scenario')
        ax.set_ylabel('Ridership Change (%)')
        ax.axhline(y=0, color='black', linestyle='-', linewidth=0.5)
        plt.xticks(rotation=45, ha='right')
        plt.tight_layout()
        
        chart_path = Path(f"ridership_impact_{config.timestamp}.png")
        plt.savefig(chart_path)
        plt.close()
        charts.append(str(chart_path))
    
    log(f"\n[green]  ✓ Simulated {len(impacts)} scenarios[/green]")
    
    return {
        'impact_df': impact_df,
        'charts': charts,
        'elasticities_used': elasticities
    }


# === Agent 3: Recommendation Agent ===
def recommendation_agent(
    impact_df: pd.DataFrame,
    budget_cap: Optional[float] = None,
    optimize: bool = False,
    config: Config = Config()
) -> Dict[str, Any]:
    """
    Agent 3: Rank scenarios and generate executive recommendations.
    Can brute-force search if optimize=True.
    """
    log("\n[bold green]💡 RECOMMENDATION AGENT[/bold green]")
    
    if impact_df.empty:
        return {
            'top_n_df': pd.DataFrame(),
            'exec_summary': "No scenarios to evaluate."
        }
    
    # Calculate composite score
    # Prioritize: ridership gain, revenue sustainability, minimal vehicle hours
    impact_df['composite_score'] = (
        impact_df['ridership_impact_pct'] * 0.5 +  # 50% weight on ridership
        impact_df['revenue_impact_pct'] * 0.3 +     # 30% weight on revenue
        -impact_df['vehicle_hours_change_pct'] * 0.2  # 20% weight on efficiency
    )
    
    # Apply budget constraints if provided
    if budget_cap:
        # Simplified: assume each 1% vehicle hour increase = $100k annual cost
        impact_df['estimated_cost_m'] = impact_df['vehicle_hours_change_pct'] * 0.1
        impact_df = impact_df[impact_df['estimated_cost_m'] <= budget_cap]
    
    # Sort by composite score
    top_scenarios = impact_df.nlargest(3, 'composite_score')[
        ['scenario', 'ridership_impact_pct', 'revenue_impact_pct', 'composite_score']
    ].round(1)
    
    # Generate executive summary
    if len(top_scenarios) > 0:
        top_scenario = top_scenarios.iloc[0]
        
        prompt = f"""
        Generate a 3-sentence executive summary for this transit scenario recommendation:
        
        Top scenario: {top_scenario['scenario']}
        Ridership impact: {top_scenario['ridership_impact_pct']:.1f}%
        Revenue impact: {top_scenario['revenue_impact_pct']:.1f}%
        
        Make it concise, action-oriented, and highlight the key benefit.
        """
        
        exec_summary = call_llm(prompt, config)
    else:
        exec_summary = "No viable scenarios found within constraints."
    
    # Optimize mode (grid search)
    if optimize and len(impact_df) > 1:
        log("  → Running optimization grid search...")
        # This is where you'd implement more sophisticated optimization
        # For demo, we'll just note it as a TODO
        exec_summary += "\n\n[Optimization mode: Advanced grid search available with --optimize flag]"
    
    log(f"\n[green]  ✓ Generated recommendations[/green]")
    
    return {
        'top_n_df': top_scenarios,
        'exec_summary': exec_summary,
        'full_ranking': impact_df.sort_values('composite_score', ascending=False)
    }


# === Report Generation ===
def generate_report(
    results: Dict[str, Any],
    output_path: Path,
    scenario_text: str,
    config: Config = Config()
) -> Path:
    """Generate markdown report with all findings"""
    
    report_path = output_path / "scenario_analysis_report.md"
    
    with open(report_path, 'w') as f:
        f.write("# Transit Scenario Analysis Report\n")
        f.write(f"*Generated: {datetime.now().strftime('%Y-%m-%d %H:%M')}*\n\n")
        
        f.write(f"## Scenario Evaluated\n")
        f.write(f"```\n{scenario_text}\n```\n\n")
        
        f.write("## 1. Current Ridership Recovery\n")
        f.write(results['analytics']['summary_txt'])
        f.write("\n\n")
        
        f.write("## 2. Scenario Impacts\n")
        if not results['simulation']['impact_df'].empty:
            f.write(results['simulation']['impact_df'].to_markdown(index=False))
        f.write("\n\n")
        
        f.write("## 3. Recommendations\n")
        f.write(results['recommendation']['exec_summary'])
        f.write("\n\n")
        
        if not results['recommendation']['top_n_df'].empty:
            f.write("### Top 3 Scenarios\n")
            f.write(results['recommendation']['top_n_df'].to_markdown(index=False))
        
        f.write("\n\n---\n*Transit Scenario Copilot v1.0*")
    
    log(f"\n[green]📄 Report saved: {report_path}[/green]")
    return report_path


# === Main Orchestration ===
def quickstart():
    """Zero-config demo run"""
    log("[bold magenta]🚀 QUICKSTART DEMO - Transit Scenario Copilot[/bold magenta]\n")
    
    config = Config()
    output_path = setup_output_dir(config)
    
    # Demo scenario
    scenario_text = "increase rail frequency 15% off-peak; reduce bus fares 20% on weekends"
    scenarios = parse_scenario_text(scenario_text)
    
    # Run pipeline
    results = {}
    
    # Agent 1
    results['analytics'] = ridership_analytics_agent(
        ridership_csv=Path(config.default_ridership) if Path(config.default_ridership).exists() else None,
        config=config
    )
    
    # Agent 2
    results['simulation'] = simulation_agent(
        scenario_json=scenarios,
        recovery_df=results['analytics']['recovery_df'],
        config=config
    )
    
    # Agent 3
    results['recommendation'] = recommendation_agent(
        impact_df=results['simulation']['impact_df'],
        budget_cap=2.0,  # $2M budget
        config=config
    )
    
    # Generate report
    report_path = generate_report(results, output_path, scenario_text, config)
    
    # Create a simple chart if possible
    if PLOTTING_AVAILABLE and not results['simulation']['impact_df'].empty:
        fig, ax = plt.subplots(figsize=(10, 6))
        
        df = results['simulation']['impact_df']
        x = df['scenario']
        y1 = df['ridership_impact_pct']
        y2 = df['revenue_impact_pct']
        
        x_pos = np.arange(len(x))
        width = 0.35
        
        bars1 = ax.bar(x_pos - width/2, y1, width, label='Ridership Impact %')
        bars2 = ax.bar(x_pos + width/2, y2, width, label='Revenue Impact %')
        
        ax.set_xlabel('Scenarios')
        ax.set_ylabel('Impact (%)')
        ax.set_title('Scenario Impact Analysis')
        ax.set_xticks(x_pos)
        ax.set_xticklabels(x, rotation=45, ha='right')
        ax.legend()
        ax.axhline(y=0, color='black', linestyle='-', linewidth=0.5)
        
        plt.tight_layout()
        chart_path = output_path / "impact_comparison.png"
        plt.savefig(chart_path)
        plt.close()
        
        log(f"[green]📊 Chart saved: {chart_path}[/green]")
    
    log(f"\n[bold green]✅ Quickstart complete! Check {output_path}/[/bold green]")
    return results


def main():
    """CLI entry point"""
    parser = argparse.ArgumentParser(
        description="Transit Scenario Copilot - AI agents for rapid transit planning",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Run quickstart demo
  python planning_copilot_demo.py quickstart
  
  # Analyze a specific scenario
  python planning_copilot_demo.py --scenario "increase rail frequency 20% peak hours"
  
  # Ask a natural language question
  python planning_copilot_demo.py --ask "Which routes have the lowest recovery?"
  
  # Optimize with budget constraint
  python planning_copilot_demo.py --scenario "..." --optimize --budget_m 5
        """
    )
    
    parser.add_argument('command', nargs='?', choices=['quickstart'], 
                       help='Run quickstart demo')
    parser.add_argument('--gtfs', type=Path, help='Path to GTFS zip file')
    parser.add_argument('--ridership', type=Path, help='Path to ridership CSV')
    parser.add_argument('--scenario', type=str, 
                       help='Scenario description (e.g., "increase rail frequency 20%")')
    parser.add_argument('--ask', type=str, help='Natural language question about ridership')
    parser.add_argument('--optimize', action='store_true', 
                       help='Run optimization to find best scenario mix')
    parser.add_argument('--budget_m', type=float, help='Budget cap in millions')
    
    args = parser.parse_args()
    
    # Handle quickstart
    if args.command == 'quickstart' or (not args.scenario and not args.ask):
        quickstart()
        return
    
    # Otherwise run with arguments
    config = Config()
    output_path = setup_output_dir(config)
    
    results = {}
    
    # Agent 1: Analytics
    results['analytics'] = ridership_analytics_agent(
        gtfs_path=args.gtfs,
        ridership_csv=args.ridership or Path(config.default_ridership),
        question=args.ask,
        config=config
    )
    
    # If we have a scenario, run simulation and recommendation
    if args.scenario:
        scenarios = parse_scenario_text(args.scenario)
        
        # Agent 2: Simulation
        results['simulation'] = simulation_agent(
            scenario_json=scenarios,
            recovery_df=results['analytics']['recovery_df'],
            config=config
        )
        
        # Agent 3: Recommendation
        results['recommendation'] = recommendation_agent(
            impact_df=results['simulation']['impact_df'],
            budget_cap=args.budget_m,
            optimize=args.optimize,
            config=config
        )
        
        # Generate report
        report_path = generate_report(results, output_path, args.scenario, config)
    
    else:
        # Just analytics question
        log(f"\n[cyan]{results['analytics']['summary_txt']}[/cyan]")
    
    log(f"\n[bold green]✅ Analysis complete! Output in {output_path}/[/bold green]")


# === TODO: Stretch Goals ===
# TODO: Equity overlay with census shapefile integration
# TODO: Sustainability calculations (kg CO2 saved per scenario)  
# TODO: Slack webhook integration for exec summaries
# TODO: Real GTFS route extraction and stop-level analysis
# TODO: Monte Carlo simulation for uncertainty bounds
# TODO: Integration with Azure AI Foundry deployment
# TODO: Copilot Studio agent packaging


if __name__ == "__main__":
    main()