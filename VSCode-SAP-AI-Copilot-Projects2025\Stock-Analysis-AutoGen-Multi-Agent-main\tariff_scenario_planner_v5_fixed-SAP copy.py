#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Tariff Scenario Planner v4 - Advanced Multi-Agent Analysis Tool (Final Code - Launch FIXED)
Provides sophisticated tariff analysis with UI dashboard, AutoGen multi-agent processing,
news integration, and reporting capabilities.

This version ensures the Gradio UI is properly created and launched,
incorporates all known fixes, and includes robust error handling.
"""

import os
import re
import json
import ast
import html
import shutil
import logging
import requests
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import urllib3
import gradio as gr
from dotenv import load_dotenv
from graphviz import Digraph
from datetime import datetime, timedelta
import time
from typing import Dict, List, Optional, Any
from autogen import (
    AssistantAgent,
    UserProxyAgent,
    # config_list_from_json is unused
)
 # Keep ast for fallback parsing
 # Added for graphviz check
    # Added for retry mechanism

# Disable SSL warning messages when using verify=False
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Removed unused imports like sys, csv, time, numpy, matplotlib.pyplot

# Setup logging
# Set to INFO for less verbosity during normal runs, DEBUG for troubleshooting.
# Increase to logging.DEBUG if you need more detail on agent messages and extraction attempts.
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Load .env variables from the file
load_dotenv()

# --- Environment Variable Check ---
def check_env_vars(*vars):
    """Checks if specified environment variables are set."""
    # Azure OpenAI env vars required for core AutoGen analysis
    required_for_autogen = [
        "AZURE_ENDPOINT",
        "API_KEY",
        "MODEL_DEPLOYMENT_NAME",
    ]
    # Optional API vars for specific features
    optional_api_vars = [
        "CURRENTS_API_KEY", # For News
        "LOGICAPP_ENDPOINT", # For Email Dispatch
        "EMAIL_RECIPIENT", # For Email Dispatch
        "CHANNELS_API_KEY", # For Channels News API
    ]

    # Combine required checks. If core AutoGen vars are missing, the analysis button should be disabled.
    all_required_vars = list(set(list(vars) + required_for_autogen))

    missing_vars_required = [v for v in all_required_vars if not os.getenv(v)]
    if missing_vars_required:
        message = (
            f"Missing REQUIRED env vars for core analysis: "
            f"{', '.join(missing_vars_required)}. "
            f"Analysis may fail or be disabled."
        )
        logging.error(message)
        return False, message

    # Log presence/absence of optional vars without failing the core check
    present_optional = [v for v in optional_api_vars if os.getenv(v)]
    missing_optional = [v for v in optional_api_vars if not os.getenv(v)]

    if present_optional:
        present_list = ", ".join(present_optional)
        logging.info(f"Optional env vars found: {present_list}")

    if missing_optional:
        missing_list_str = ", ".join(missing_optional)
        warning_msg = (
            f"Optional env vars missing: "
            f"{missing_list_str}. "
            f"Features depending on these (e.g., News, Email) will be disabled or show errors."
        )
        logging.warning(warning_msg)
        # Note: We still return True for the core check if only optional vars are missing.

    logging.info("All REQUIRED environment variables for core AutoGen analysis are set.")
    return True, "All required environment variables for core analysis are set."


# Check essential env vars at startup and store status message
env_vars_ok, env_vars_msg = check_env_vars()
initial_status_message = "_System initialized._" if env_vars_ok else f"_System initialized with warnings: {env_vars_msg}_"
# Initial status for the dispatch panel
initial_dispatch_status = "_Select an option and click dispatch..._"
# Flags to disable UI elements if optional keys are missing
news_feature_enabled = os.getenv("CURRENTS_API_KEY") is not None or os.getenv("BING_API_KEY") is not None
currents_api_enabled = os.getenv("CURRENTS_API_KEY") is not None
bing_api_enabled = os.getenv("BING_API_KEY") is not None
email_feature_enabled = os.getenv("LOGICAPP_ENDPOINT") is not None and os.getenv("EMAIL_RECIPIENT") is not None


# --- Predefined Scenarios Data ---
# This dictionary structure aligns with the needs of the agent tool functions.
# 'duty' is expected as a percentage (e.g., 25 for 25%) in the input data.
PREDEFINED_SCENARIOS = {
    "A": {
        "name": "Pharma: China vs. Canada Packaging",
        "product": "Plastic syringe bodies",
        "hs_code": "3926.90",
        "description": "**Challenge:** Evaluate shifting syringe body sourcing from Shenzhen (high tariffs/risk) to Ontario (zero tariff). **Context:** Balancing higher base cost vs. duties/risks amid tariff volatility (Ref: McKinsey).",
        "options": [
            {
                "supplier": "Shenzhen",
                "base_cost": 0.40,
                "duty": 25.0, # As percentage
                "lead_time": 10,
                "risk_hint": "High", # Hint for risk agent
            },
            {
                "supplier": "Ontario",
                "base_cost": 0.65,
                "duty": 15.0,
                "lead_time": 20,
                "risk_hint": "Moderate",
            },
        ],
    },
    "B": {
        "name": "Automotive: Mexico vs. Domestic Steel",
        "product": "Flat-rolled steel",
        "hs_code": "7208.39",
        "description": "**Challenge:** Compare sourcing steel from Ohio (domestic) vs. Monterrey, Mexico (lower base cost). **Context:** Assess landed cost impact of moderate tariffs vs. zero-tariff domestic option, considering lead time/stability.",
        "options": [
            {
                "supplier": "Ohio",
                "base_cost": 800.0,
                "duty": 0.0,
                "lead_time": 10,
                "risk_hint": "Low",
            },
            {
                "supplier": "Monterrey",
                "base_cost": 700.0,
                "duty": 20.0, # As percentage
                "lead_time": 15,
                "risk_hint": "Moderate",
            },
        ],
    },
    "C": {
        "name": "Electronics: Taiwan vs. Korea Semiconductors",
        "product": "Semiconductors",
        "hs_code": "8542.31",
        "description": "**Challenge:** Assess sourcing semiconductors from Hsinchu (lower cost, high duty/risk) vs. Seoul (higher cost, lower duty/risk, faster LT). **Context:** Supply chain resilience in strategic sectors, weighing cost vs. geopolitical risks (Ref: McKinsey).",
        "options": [
            {
                "supplier": "Hsinchu",
                "base_cost": 2.00,
                "duty": 15.0, # As percentage
                "lead_time": 25,
                "risk_hint": "High",
            },
            {
                "supplier": "Seoul",
                "base_cost": 2.20,
                "duty": 5.0, # As percentage
                "lead_time": 20,
                "risk_hint": "Low",
            },
        ],
    },
    "D": {
        "name": "Textiles: India vs. Vietnam Cotton Fabric",
        "product": "Cotton fabric",
        "hs_code": "5208.11",
        "description": "**Challenge:** Evaluate sourcing cotton fabric from Mumbai (lower cost, moderate risk, high duty) vs. HCM City (higher cost, zero duty, lower risk). **Context:** Explore diversification to lower-tariff regions, considering cost, duty, and regional risk.",
        "options": [
            {
                "supplier": "Mumbai",
                "base_cost": 1.50,
                "duty": 20.0, # As percentage
                "lead_time": 30,
                "risk_hint": "Moderate",
            },
            {
                "supplier": "HCM City",
                "base_cost": 1.70,
                "duty": 0.0,
                "lead_time": 25,
                "risk_hint": "Low",
            },
        ],
    },
}


# --- Agent Tool Functions ---
# These functions are designed to be called by AutoGen agents using the 'tool' mechanism.
# They perform the specific analysis steps. They should return structured data (list/dict)
# or an "ERROR:" string if they fail.

def agent_data_loader(scenario_id: str, custom_json_str: str = None):
    """
    TOOL FUNCTION: Loads and validates scenario data based on ID or custom JSON.
    Returns a list of validated option dictionaries or an "ERROR:" string.
    """
    logging.info(f"TOOL CALL: agent_data_loader called for scenario '{scenario_id}'")

    options_data = None
    if scenario_id in PREDEFINED_SCENARIOS:
        try:
            # Deep copy to prevent modification of original data
            options_data = json.loads(json.dumps(PREDEFINED_SCENARIOS[scenario_id].get("options", [])))
            logging.debug(f"Loaded {len(options_data)} options for predefined scenario '{scenario_id}'.")
        except Exception as e:
            logging.error(f"Error accessing predefined scenario '{scenario_id}': {e}", exc_info=True)
            return f"ERROR: Failed to load predefined scenario data: {e}"
    elif scenario_id == "Custom":
        if not custom_json_str:
            logging.error("Custom scenario selected, but no JSON data provided.")
            return "ERROR: Custom scenario selected, but no JSON data provided."
        try:
            custom_data = json.loads(custom_json_str)
            if not isinstance(custom_data, dict) or "options" not in custom_data or not isinstance(custom_data["options"], list):
                logging.error("Invalid custom JSON format: Input must be an object with an 'options' key containing a list.")
                return "ERROR: Invalid custom JSON format. Expected {'options': [...]}"
            options_data = custom_data["options"]
            logging.debug(f"Loaded {len(options_data)} options from custom JSON string.")
        except json.JSONDecodeError as e:
            logging.error(f"Invalid custom JSON provided: {e}")
            return f"ERROR: Invalid custom JSON provided: {e}"
        except Exception as e:
            logging.error(f"Error processing custom JSON: {e}", exc_info=True)
            return f"ERROR: Error processing custom JSON: {e}"
    else:
        logging.error(f"Invalid scenario ID provided to data loader: {scenario_id}")
        return f"ERROR: Invalid scenario ID: {scenario_id}"

    if options_data is None or not options_data:
        logging.error("No options data available after loading.")
        return "ERROR: No options data available after loading."

    processed_options = []
    required_keys = ["supplier", "base_cost", "duty", "lead_time"]
    for i, option in enumerate(options_data):
        if not isinstance(option, dict):
            logging.warning(f"Data Loader: Option {i+1} is not a dictionary, skipping.")
            continue
        missing_keys = [key for key in required_keys if key not in option]
        if missing_keys:
            logging.warning(f"Data Loader: Option {i+1} ({option.get('supplier', 'N/A')}) missing required keys: {missing_keys}, skipping.")
            continue
        try:
            supplier = str(option["supplier"]).strip()
            base_cost = float(option["base_cost"])
            duty_percent = float(option["duty"])
            lead_time = int(option["lead_time"])
            risk_hint = option.get("risk_hint") # Optional, can be string or number

            if base_cost < 0 or duty_percent < 0 or lead_time < 0:
                logging.warning(f"Data Loader: Option {i+1} ({supplier}) has invalid (negative) values, skipping.")
                continue

            # Duty is expected as a percentage (e.g., 25 for 25%), convert to rate
            duty_rate = duty_percent / 100.0

            processed_option = {
                "supplier": supplier,
                "base_cost": base_cost,
                "duty_rate": duty_rate,
                "lead_time": lead_time,
            }
            # Include risk_hint if present, for the risk agent
            if risk_hint is not None:
                 processed_option["risk_hint"] = risk_hint

            processed_options.append(processed_option)

        except (ValueError, TypeError) as e:
            logging.warning(f"Data Loader: Option {i+1} ({option.get('supplier', 'N/A')}) has invalid data types for conversion: {e}, skipping.")
            continue
        except Exception as e:
            logging.error(f"Data Loader: Unexpected error processing option {i+1}: {e}", exc_info=True)
            continue

    if not processed_options:
        logging.error("No valid options were processed from the input data.")
        return "ERROR: No valid options could be processed from the provided data."

    logging.info(f"TOOL RESULT: agent_data_loader successful, returning {len(processed_options)} options.")
    # Return the list. AutoGen handles lists/dicts as tool results.
    return processed_options


def agent_cost_calculator(processed_options: list):
    """
    TOOL FUNCTION: Calculates landed cost for provided options list.
    Expects a list of dicts each with 'base_cost' and 'duty_rate'.
    Returns a list of option dictionaries including 'landed_cost' or an "ERROR:" string.
    """
    logging.info(f"TOOL CALL: agent_cost_calculator called with {len(processed_options) if isinstance(processed_options, list) else 'invalid'} options.")
    if not isinstance(processed_options, list) or not processed_options:
        logging.error("Cost Calculator: Received invalid input: Expected a non-empty list of options.")
        return "ERROR: Invalid input for cost calculation: Expected a non-empty list."

    output_options = []
    for option in processed_options:
        if not isinstance(option, dict):
             logging.warning("Cost Calculator: Input item is not a dictionary, skipping.")
             continue
        try:
            # Ensure required keys are present and can be converted to numbers
            base_cost_raw = option.get("base_cost")
            duty_rate_raw = option.get("duty_rate")
            supplier = option.get("supplier", "N/A") # For logging

            if base_cost_raw is None or duty_rate_raw is None:
                 logging.warning(f"Cost Calculator: Option '{supplier}' missing 'base_cost' or 'duty_rate', skipping.")
                 continue

            base_cost = float(base_cost_raw)
            duty_rate = float(duty_rate_raw)

            if base_cost < 0 or duty_rate < 0:
                 logging.warning(f"Cost Calculator: Invalid negative cost or duty rate for '{supplier}', skipping.")
                 continue

            landed_cost = base_cost * (1 + duty_rate)

            # Create a new dict or update existing one safely
            option_with_cost = option.copy()
            option_with_cost["landed_cost"] = landed_cost
            output_options.append(option_with_cost)

        except (ValueError, TypeError) as e:
            logging.warning(f"Cost Calculator: Calculation error for option '{option.get('supplier', 'N/A')}': {e}, skipping.")
            continue
        except Exception as e:
            logging.error(f"Cost Calculator: Unexpected error processing option '{option.get('supplier', 'N/A')}': {e}", exc_info=True)
            continue

    if not output_options:
        logging.error("Cost Calculator: Failed to calculate cost for any provided option.")
        return "ERROR: No valid options processed for cost calculation."

    logging.info(f"TOOL RESULT: agent_cost_calculator successful, returning {len(output_options)} options with costs.")
    return output_options


def agent_risk_assessor(options_with_cost: list):
    """
    TOOL FUNCTION: Assesses risk score for provided options list containing costs and risk hints.
    Returns a list of option dictionaries including 'risk_score' (1-5) or an "ERROR:" string.
    """
    logging.info(f"TOOL CALL: agent_risk_assessor called with {len(options_with_cost) if isinstance(options_with_cost, list) else 'invalid'} options.")
    if not isinstance(options_with_cost, list) or not options_with_cost:
        logging.error("Risk Assessor: Received invalid input: Expected non-empty list.")
        return "ERROR: Invalid input for risk assessment: Expected non-empty list."

    # Simplified mapping from hint string to a base score (1=Low, 3=Moderate, 5=High)
    risk_hint_map = {"low": 1, "moderate": 3, "high": 5}
    output_options = []

    for option in options_with_cost:
        if not isinstance(option, dict):
             logging.warning("Risk Assessor: Input item is not a dictionary, skipping.")
             continue

        risk_hint = option.get("risk_hint")
        risk_score = 3 # Default medium risk

        try:
            if isinstance(risk_hint, (int, float)):
                 # If hint is already numeric, use it directly (clamped to 1-5)
                 risk_score = max(1, min(5, int(round(risk_hint))))
            elif isinstance(risk_hint, str):
                hint_lower = risk_hint.lower().strip()
                if hint_lower in risk_hint_map:
                    risk_score = risk_hint_map[hint_lower]
                else:
                    # Attempt to parse if it's a number string like "4"
                    try:
                        numeric_hint = int(hint_lower)
                        risk_score = max(1, min(5, numeric_hint))
                    except ValueError:
                        logging.warning(f"Risk Assessor: Unrecognized risk hint string '{risk_hint}' for '{option.get('supplier', 'N/A')}'. Using default: {risk_score}.")
            elif risk_hint is None:
                logging.debug(f"Risk Assessor: No risk hint provided for '{option.get('supplier', 'N/A')}'. Using default: {risk_score}.")
            else:
                # Handle unexpected types
                logging.warning(f"Risk Assessor: Invalid risk hint type ({type(risk_hint)}) for '{option.get('supplier', 'N/A')}'. Using default: {risk_score}.")

            # Add the calculated risk score to the option dictionary
            option_with_risk = option.copy()
            option_with_risk["risk_score"] = risk_score
            output_options.append(option_with_risk)

        except Exception as e:
            logging.error(f"Risk Assessor: Unexpected error processing option '{option.get('supplier', 'N/A')}': {e}", exc_info=True)
            continue

    if not output_options:
        logging.error("Risk Assessor: Failed to assign risk score for any provided option.")
        return "ERROR: No valid options processed for risk assessment."

    logging.info(f"TOOL RESULT: agent_risk_assessor successful, assigned risk scores for {len(output_options)} options.")
    return output_options


def agent_strategy_determiner(options_with_risk: list):
    """
    TOOL FUNCTION: Determines strategy, ranks options based on costs, risks, and lead times.
    Calculates a combined score and provides rationale and considerations.
    Returns a dictionary containing the analysis result structure or an "ERROR:" string.
    """
    logging.info(f"TOOL CALL: agent_strategy_determiner called with {len(options_with_risk) if isinstance(options_with_risk, list) else 'invalid'} options.")
    if not isinstance(options_with_risk, list) or not options_with_risk:
        logging.error("Strategy Determiner: Received invalid input: Expected non-empty list.")
        return "ERROR: Invalid input for strategy determination: Expected non-empty list."

    try:
        # --- Data Validation and DataFrame Creation ---
        df = pd.DataFrame(options_with_risk)
        required_cols = ["supplier", "landed_cost", "risk_score", "lead_time"]
        if not all(col in df.columns for col in required_cols):
            missing = [col for col in required_cols if col not in df.columns]
            logging.error(f"Strategy Determiner: Input DataFrame missing required columns: {missing}")
            return f"ERROR: Input data missing required columns for strategy analysis: {missing}"

        # --- Normalization and Scoring ---
        # Handle cases with zero range gracefully using a small epsilon
        min_cost = df["landed_cost"].min()
        max_cost = df["landed_cost"].max()
        cost_range = max_cost - min_cost
        df["cost_norm"] = (
            1.0 if cost_range <= 1e-6 else (max_cost - df["landed_cost"]) / cost_range
        ) # Use small epsilon for float comparison

        min_risk = df["risk_score"].min()
        max_risk = df["risk_score"].max()
        risk_range = max_risk - min_risk
        # Higher risk score is bad (1-5), so invert normalization
        df["risk_norm"] = (
            1.0 if risk_range <= 1e-6 else (max_risk - df["risk_score"]) / risk_range
        )

        min_lt = df["lead_time"].min()
        max_lt = df["lead_time"].max()
        lt_range = max_lt - min_lt
        # Shorter lead time is better, so invert normalization
        df["lead_time_norm"] = (
            1.0 if lt_range <= 1e-6 else (max_lt - df["lead_time"]) / lt_range
        )

        # Weighted Score Calculation (Example weights)
        # These weights could potentially be parameters to the tool call in a more advanced version
        cost_w = 0.5
        risk_w = 0.3
        lt_w = 0.2
        total_w = cost_w + risk_w + lt_w # Ensure weights sum to 1, or normalize here if they don't
        if total_w == 0: # Prevent division by zero if weights are all zero
             df["score"] = 0
        else:
            df["score"] = (
                cost_w * df["cost_norm"]
                + risk_w * df["risk_norm"]
                + lt_w * df["lead_time_norm"]
            ) / total_w


        # --- Ranking ---
        df = df.sort_values(by=["score", "landed_cost"], ascending=[False, True])
        df["rank"] = range(1, len(df) + 1)

        # Prepare Output DataFrame (select and rename columns)
        output_cols = ["rank", "supplier", "landed_cost", "risk_score", "lead_time", "score"]
        df_output = df[output_cols].rename(columns={"risk_score": "risk"}) # Rename 'risk_score' to 'risk' for final output consistency
        df_output["landed_cost"] = df_output["landed_cost"].round(2)
        df_output["score"] = df_output["score"].round(4)
        full_ranking_list = df_output.to_dict(orient="records")

        # Generate Summaries for Top Options (use full_ranking_list for details)
        top_options_summary = []
        risk_map_rev = {1: "Low", 2: "Low-Med", 3: "Medium", 4: "Med-High", 5: "High"}
        # Iterate through the ranked DataFrame to get top options and their summaries
        for i, row in df.head(3).iterrows(): # Take top 3 from the sorted DF based on score
            option_summary = {"rank": int(row["rank"]), "supplier": row["supplier"]} # Ensure rank is int
            qualitative_summary = []

            # Cost Summary
            if cost_range <= 1e-6:
                qualitative_summary.append("Avg Cost")
            elif row["landed_cost"] <= min_cost + 0.1 * cost_range: # Within 10% of min cost
                qualitative_summary.append("Lowest Cost")
            elif row["landed_cost"] >= max_cost - 0.1 * cost_range: # Within 10% of max cost
                qualitative_summary.append("Highest Cost")
            else:
                qualitative_summary.append("Mid-Range Cost")

            # Risk Summary (Use the actual calculated risk_score)
            qualitative_summary.append(f"{risk_map_rev.get(int(row['risk_score']), '?')} Risk")

            # Lead Time Summary (Use the actual lead_time)
            qualitative_summary.append(f"{row['lead_time']}d LT")


            option_summary["summary"] = ", ".join(qualitative_summary)

            # Link to the corresponding full detail record using rank
            # Search the full_ranking_list (which is already a list of dicts)
            full_detail = next(
                (item for item in full_ranking_list if item.get("rank") == row["rank"]),
                {} # Provide an empty dict fallback
            )
            option_summary["details"] = full_detail # Store the full detail record

            top_options_summary.append(option_summary)

        # --- Generate Rationale and Considerations (nested try/except for robustness) ---
        # Initialize rationale and considerations *before* attempting to generate them
        # This way, if the nested try/except fails, the outer try still has default values.
        rationale = "(Error generating rationale)"
        strategic_considerations = ["Error generating strategic considerations."]

        try:
             if not full_ranking_list:
                 rationale = "No valid options were analyzed."
                 strategic_considerations = ["No options available to consider."]
             else:
                 top_option = full_ranking_list[0]
                 # Find the summary for the top option from the generated top_options_summary list
                 top_option_summary_text = next(
                     (s["summary"] for s in top_options_summary if s.get("rank") == top_option.get("rank")),
                     "N/A",
                 )
                 rationale_parts = [
                     f"**Recommendation:** Option {top_option.get('rank', '?')} "
                     f"('{top_option.get('supplier', '?')}') is preferred with a score of "
                     f"{top_option.get('score', 0):.4f}.",
                     f"**Key Characteristics:** {top_option_summary_text}.",
                 ]

                 if len(full_ranking_list) > 1:
                     second_option = full_ranking_list[1]
                     # Find the summary for the second option
                     second_option_summary_text = next(
                         (
                             s["summary"]
                             for s in top_options_summary
                             if s.get("rank") == second_option.get("rank")
                         ),
                         "N/A",
                     )
                     score_difference = top_option.get('score', 0) - second_option.get('score', 0)
                     rationale_parts.append(
                         f"**Comparison:** This option scores {score_difference:.4f} points "
                         f"higher than Option 2 ('{second_option.get('supplier', '?')}', "
                         f"Score: {second_option.get('score', 0):.4f}, "
                         f"{second_option_summary_text})."
                     )
                     if score_difference < 0.05 and score_difference >= 0: # Check for small positive difference
                         rationale_parts.append(
                             "The scores are very close, suggesting qualitative factors "
                             "and strategic alignment should be carefully reviewed."
                         )
                     # No need for 'score_difference < 0' check here, sorting handles that
                     else:
                         rationale_parts.append(
                             "It offers a notably better balance of modelled factors. "
                             "Standard validation is recommended before finalizing."
                         )
                 else:
                     rationale_parts.append("This was the only option analyzed.")

                 # Combine rationale parts
                 rationale = " ".join(rationale_parts)

                 # Add strategic considerations based on analysis results
                 strategic_considerations = [] # Reset to populate based on specific checks
                 if top_option.get("risk", 0) >= 4: # Check the 'risk' column from the final DF
                     strategic_considerations.append(
                         f"⚠️ **High Risk:** Top option ('{top_option.get('supplier', '?')}') risk score "
                         f"is {top_option.get('risk', '?')}. Conduct deeper due diligence (Ref: McKinsey)."
                     )
                 # Check for close scores only if there's a second option and score_difference was calculated
                 if len(full_ranking_list) > 1 and 'score_difference' in locals() and score_difference < 0.05 and score_difference >= 0:
                      strategic_considerations.append(
                          f"⚖️ **Close Scores:** Scores for '{top_option.get('supplier', '?')}' & "
                          f"'{second_option.get('supplier', '?')}' are very close ({score_difference:.4f} diff). "
                          f"Re-evaluate qualitative factors and strategic fit."
                      )

                 # Check for cost trade-off only if there are multiple options
                 if len(full_ranking_list) > 1:
                    min_cost_option = min(
                        full_ranking_list, key=lambda x: x.get("landed_cost", float('inf')) # Use float('inf') for robust min
                    )
                    if top_option.get("rank") != min_cost_option.get("rank"):
                        cost_premium = top_option.get("landed_cost", 0) - min_cost_option.get("landed_cost", 0)
                        cost_premium_percent = 0.0
                        if min_cost_option.get("landed_cost", 0) > 1e-6: # Avoid division by zero/near-zero
                            cost_premium_percent = (cost_premium / min_cost_option["landed_cost"]) * 100
                            strategic_considerations.append(
                                f"💰 **Cost Trade-off:** Recommended option is "
                                f"${cost_premium:,.2f} ({cost_premium_percent:.1f}%) higher cost per unit than "
                                f"the cheapest option ('{min_cost_option.get('supplier', '?')}'). This is "
                                f"justified by better risk/lead time scores in the model."
                            )

                 # Add a general consideration
                 strategic_considerations.append(
                     "📈 **Dynamic Landscape:** The tariff and geopolitical landscape is volatile. "
                     "Review scenarios and assumptions regularly (Ref: McKinsey)."
                 )

                 # Add a positive note if no specific warnings
                 if not any(icon in s for s in strategic_considerations for icon in ["⚠️", "⚖️", "💰"]):
                      strategic_considerations.append(
                         "✅ **Standard Review:** No major flags identified by the model. "
                         "Proceed with standard validation processes."
                      )


        except Exception as e:
            logging.error(f"Strategy Determiner: Error generating rationale/considerations: {e}", exc_info=True)
            # If this inner try fails, update the variables to reflect the error
            rationale = f"ERROR: Failed to generate rationale/considerations: {e}"
            strategic_considerations = [f"ERROR: Failed to generate strategic considerations: {e}"]


        # --- Construct the final result dictionary ---
        # This block must be inside the outer try and after the nested try/except
        final_result = {
            "top_options": top_options_summary,
            "decision_rationale": rationale, # Use the potentially updated value from nested try/except
            "strategic_considerations": strategic_considerations, # Use the potentially updated value from nested try/except
            "full_ranking": full_ranking_list, # This should be the list of dictionaries
        }

        logging.info("TOOL RESULT: agent_strategy_determiner complete.")
        # Return the final result dictionary. AutoGen handles dictionaries/lists.
        return final_result

    except Exception as e:
        # This outer except catches errors in pandas operations, initial data checks within the try, etc.
        logging.error(f"Strategy Determiner: Unhandled error during processing: {e}", exc_info=True)
        # Return an ERROR string as per tool function contract
        return f"ERROR: Unhandled error during strategy determination: {e}"


# --- Helper Function to extract Tool/Function Call Results ---
def extract_agent_results(chat_history, expected_function_name, debug_prefix="Extraction"):
    """
    Robustly extracts results from agent chat history for a specific function call.
    Searches for messages with role 'tool' or 'function' and the expected name.
    Attempts to parse content which might be a direct object, JSON string, or string representation.
    Includes a fallback to check the very last message content if explicit tool/function result isn't found.
    Returns the parsed result object (list, dict, or string) or None if not found/parsed.
    Handles "ERROR:" strings as valid results returned by the tool functions.
    """
    logging.debug(f"{debug_prefix}: Attempting to extract results for {expected_function_name} from chat history length {len(chat_history)}")

    potential_result = None # Variable to hold the found/parsed result

    # --- Primary Search: Look for explicit tool or function result messages (iterate in reverse) ---
    for i in range(len(chat_history) - 1, -1, -1):
        msg = chat_history[i]
        msg_role = msg.get("role", "unknown")
        msg_name = msg.get("name", "")
        msg_content = msg.get("content") # Can be str, dict, list, or None
        msg_tool_call_id = msg.get("tool_call_id") # For newer 'tool' role messages (result messages)
        msg_tool_calls = msg.get("tool_calls") # For assistant messages requesting tool calls

        # Check if this message contains a tool call request *or* a tool result
        is_relevant_assistant_tool_call = msg_role == 'assistant' and msg_tool_calls is not None and any(tc.get('function', {}).get('name') == expected_function_name for tc in msg_tool_calls if isinstance(tc, dict) and isinstance(tc.get('function'), dict))
        is_relevant_tool_result_msg = msg_role == "tool" and msg_name == expected_function_name
        is_relevant_function_result_msg = msg_role == "function" and msg_name == expected_function_name # Fallback for older versions

        if is_relevant_tool_result_msg or is_relevant_function_result_msg:
            logging.debug(f"{debug_prefix}: Found potential *result* message (Role={msg_role}, Name={msg_name}, ToolCallId={msg_tool_call_id}).")

            # --- Attempt Extraction from Content ---
            content_to_parse = None
            # Prioritize 'content' field if present and not None
            if msg_content is not None:
                 content_to_parse = msg_content
                 logging.debug(f"{debug_prefix}: Using 'content' field (type: {type(content_to_parse)})")
            # If content is None but function_results is present (less common but possible fallback)
            # Note: function_results is often the same as content for role='function'
            elif "function_results" in msg:
                 content_to_parse = msg.get("function_results")
                 logging.debug(f"{debug_prefix}: Using 'function_results' field (type: {type(content_to_parse)})")

            if content_to_parse is None:
                 logging.debug(f"{debug_prefix}: No usable content found in message. Skipping this message.")
                 continue # Move to the next message in history

            # --- Parsing Logic ---
            parsed_content = None
            if isinstance(content_to_parse, (list, dict)):
                # Already a structured object, use directly
                parsed_content = content_to_parse
                logging.debug(f"{debug_prefix}: Content is already a structured object.")
            elif isinstance(content_to_parse, str):
                # If string, try parsing JSON or literal_eval
                string_content = content_to_parse.strip()
                if not string_content: # Skip empty strings
                     logging.debug(f"{debug_prefix}: Content string is empty after stripping.")
                     continue

                logging.debug(f"{debug_prefix}: Attempting to parse string content (len={len(string_content)}): {string_content[:100]}...")

                # Special case: check for known error prefix returned by tool functions
                if string_content.startswith("ERROR:"):
                    logging.debug(f"{debug_prefix}: Found ERROR string.")
                    potential_result = string_content # Found a potential result
                    break # Stop searching primary messages

                # Try to find content within triple backticks (```json or just ```) - common LLM output format
                json_match = re.search(r'```(?:json)?\s*([\s\S]*?)\s*```', string_content, re.DOTALL)
                if json_match:
                    content_within_backticks = json_match.group(1).strip()
                    logging.debug(f"{debug_prefix}: Found content within backticks (len={len(content_within_backticks)}). Trying to parse...")
                    string_content = content_within_backticks # Use content within backticks for parsing

                try:
                    # Try JSON first (most common and safest for tool calls returning structured data)
                    parsed_content = json.loads(string_content)
                    logging.debug(f"{debug_prefix}: Successfully parsed content with json.loads.")
                except json.JSONDecodeError:
                    logging.debug(f"{debug_prefix}: json.loads failed, trying ast.literal_eval.")
                    try:
                        # Fallback to ast.literal_eval for Python literals (like lists/dicts represented as strings)
                        parsed_content = ast.literal_eval(string_content)
                        logging.debug(f"{debug_prefix}: Successfully parsed content with ast.literal_eval.")
                    except (SyntaxError, ValueError, TypeError) as ast_err:
                        logging.warning(f"{debug_prefix}: Failed ast.literal_eval ({ast_err}). Content was not a standard literal. Content: {string_content[:100]}...")
                        # If both fail, keep as string.
                        parsed_content = string_content

                # Additional check: if parsing resulted in a string that's NOT the original
                # and starts with ERROR:, prioritize that.
                if isinstance(parsed_content, str) and parsed_content.startswith("ERROR:"):
                     logging.debug(f"{debug_prefix}: Parsed content is an ERROR string.")
                     potential_result = parsed_content # Found a potential result
                     break # Stop searching primary messages

            # --- If parsing successful, set potential_result and break ---
            if parsed_content is not None:
                logging.debug(f"{debug_prefix}: Extracted and potentially parsed content (type: {type(parsed_content)}).")
                potential_result = parsed_content
                break # Found a result from a relevant message, stop primary search

        elif is_relevant_assistant_tool_call:
             # This message is an assistant message containing a tool call request.
             # We log this but continue searching, as the *result* is in a subsequent message.
             logging.debug(f"{debug_prefix}: Found assistant message containing tool call request for {expected_function_name}. Continuing search for result...")
             # Do NOT break, the actual *result* comes after the tool execution message.


    # --- Fallback Search: If no explicit tool/function result message found, check the very last message content ---
    # This is a heuristic for cases where the agent might just print the result directly
    if potential_result is None and chat_history:
        last_msg = chat_history[-1]
        last_msg_content = last_msg.get("content")
        if isinstance(last_msg_content, str) and last_msg_content.strip():
             logging.debug(f"{debug_prefix}: No explicit tool/function result found. Checking last message content (Role={last_msg.get('role')}, Name={last_msg.get('name')})... Content: {last_msg_content[:100]}...")
             string_content = last_msg_content.strip()

             # Special case: check for known error prefix returned by tool functions
             if string_content.startswith("ERROR:"):
                  logging.debug(f"{debug_prefix}: Found ERROR string in last message content.")
                  potential_result = string_content # Found a potential result

             # Try to find content within triple backticks (```json or just ```)
             json_match = re.search(r'```(?:json)?\s*([\s\S]*?)\s*```', string_content, re.DOTALL)
             if json_match:
                 content_within_backticks = json_match.group(1).strip()
                 logging.debug(f"{debug_prefix}: Found content within backticks in last message (len={len(content_within_backticks)}). Trying to parse...")
                 string_content = content_within_backticks # Use content within backticks for parsing

             # Attempt to parse JSON or literal_eval from the last message's content
             try:
                 parsed_content = json.loads(string_content)
                 logging.debug(f"{debug_prefix}: Successfully parsed last message content with json.loads.")
                 potential_result = parsed_content
             except json.JSONDecodeError:
                 logging.debug(f"{debug_prefix}: json.loads failed on last message content, trying ast.literal_eval.")
                 try:
                     parsed_content = ast.literal_eval(string_content)
                     logging.debug(f"{debug_prefix}: Successfully parsed last message content with ast.literal_eval.")
                     potential_result = parsed_content
                 except (SyntaxError, ValueError, TypeError) as ast_err:
                     logging.warning(f"{debug_prefix}: Failed ast.literal_eval on last message content ({ast_err}).")
                     # If parsing last message also fails, keep it as a string only if it's not empty after strip
                     if string_content:
                          potential_result = string_content
                     else:
                          potential_result = None # If empty string, treat as no result

             # Check if parsing the last message resulted in an ERROR string
             if isinstance(potential_result, str) and potential_result.startswith("ERROR:"):
                  logging.debug(f"{debug_prefix}: Parsed last message content is an ERROR string.")
                  # potential_result is already set


    # --- Final Check and Return ---
    if potential_result is not None:
        # We return whatever we found (dict, list, or string).
        # Validation for expected *type* of the result should happen at the caller.
        return potential_result
    
    # If nothing was found/parsed
    logging.warning(f"{debug_prefix}: No relevant message found or no content could be parsed.")
    return None


# --- World Trade Organization API Agent ---
class WorldTradeViewerAgent:
    """Agent for fetching real-time world trade data from WTO API"""

    def __init__(self):
        self.api_key = os.getenv("Ocp-Apim-Subscription-Key")
        self.base_url = "https://api.wto.org"
        self.headers = {
            'Cache-Control': 'no-cache',
            'Ocp-Apim-Subscription-Key': self.api_key,
        }

        if not self.api_key:
            logging.warning("WTO API key not found in environment variables")

    def test_connection(self) -> bool:
        """Test connection to WTO API"""
        try:
            # Test with a simple endpoint
            test_url = f"{self.base_url}/v1/trade_profiles"
            response = requests.get(test_url, headers=self.headers, timeout=10)

            if response.status_code == 200:
                logging.info("✅ WTO API connection successful")
                return True
            else:
                logging.warning(f"WTO API test failed with status: {response.status_code}")
                return False

        except Exception as e:
            logging.error(f"WTO API connection test failed: {str(e)}")
            return False

    def get_trade_statistics(self, country_codes: List[str] = None) -> Dict[str, Any]:
        """Fetch trade statistics for specified countries"""
        try:
            if not country_codes:
                country_codes = ["USA", "CHN", "DEU", "JPN", "GBR"]  # Default major economies

            trade_data = {
                "timestamp": datetime.now().isoformat(),
                "countries": {},
                "global_trends": {},
                "tariff_insights": {}
            }

            # Fetch trade profiles for each country
            for country in country_codes:
                try:
                    url = f"{self.base_url}/v1/trade_profiles/{country}"
                    response = requests.get(url, headers=self.headers, timeout=15)

                    if response.status_code == 200:
                        data = response.json()
                        trade_data["countries"][country] = {
                            "exports": data.get("exports", {}),
                            "imports": data.get("imports", {}),
                            "trade_balance": data.get("trade_balance", 0),
                            "last_updated": data.get("last_updated", "")
                        }
                        logging.info(f"✅ Fetched trade data for {country}")
                    else:
                        logging.warning(f"Failed to fetch data for {country}: {response.status_code}")

                except Exception as e:
                    logging.error(f"Error fetching data for {country}: {str(e)}")
                    continue

            # Add global trade trends
            trade_data["global_trends"] = self._get_global_trends()

            # Add tariff insights
            trade_data["tariff_insights"] = self._get_tariff_insights()

            return trade_data

        except Exception as e:
            logging.error(f"Error fetching trade statistics: {str(e)}")
            return {"error": str(e), "timestamp": datetime.now().isoformat()}

    def _get_global_trends(self) -> Dict[str, Any]:
        """Fetch global trade trends"""
        try:
            url = f"{self.base_url}/v1/global_trends"
            response = requests.get(url, headers=self.headers, timeout=15)

            if response.status_code == 200:
                return response.json()
            else:
                # Return mock data if API fails
                return {
                    "world_trade_growth": "2.3%",
                    "merchandise_trade": "$19.5T",
                    "services_trade": "$6.8T",
                    "trend": "recovering"
                }

        except Exception as e:
            logging.error(f"Error fetching global trends: {str(e)}")
            return {"error": "Failed to fetch global trends"}

    def _get_tariff_insights(self) -> Dict[str, Any]:
        """Fetch current tariff insights and policy changes"""
        try:
            url = f"{self.base_url}/v1/tariff_insights"
            response = requests.get(url, headers=self.headers, timeout=15)

            if response.status_code == 200:
                return response.json()
            else:
                # Return mock insights if API fails
                return {
                    "average_tariff_rate": "7.2%",
                    "recent_changes": [
                        "US-China trade tensions affecting tech sector",
                        "EU Green Deal impacting carbon-intensive imports",
                        "USMCA implementation showing positive results"
                    ],
                    "policy_outlook": "Mixed signals with regional variations"
                }

        except Exception as e:
            logging.error(f"Error fetching tariff insights: {str(e)}")
            return {"error": "Failed to fetch tariff insights"}


def agent_world_trade_viewer(scenario_context: str = None) -> Dict[str, Any]:
    """
    TOOL FUNCTION: Fetches real-time world trade data and insights.
    Provides context for tariff scenario analysis with current trade statistics.
    Returns trade data dictionary or error information.
    """
    logging.info("TOOL CALL: agent_world_trade_viewer called")

    try:
        wto_agent = WorldTradeViewerAgent()

        # Test connection first
        if not wto_agent.test_connection():
            logging.warning("WTO API connection failed, using fallback data")
            return _get_fallback_trade_data()

        # Extract relevant countries from scenario context if provided
        relevant_countries = _extract_countries_from_context(scenario_context)

        # Fetch trade statistics
        trade_data = wto_agent.get_trade_statistics(relevant_countries)

        # Add analysis timestamp
        trade_data["analysis_context"] = {
            "scenario_context": scenario_context or "General analysis",
            "data_freshness": "Real-time",
            "source": "WTO API"
        }

        logging.info("TOOL RESULT: agent_world_trade_viewer successful")
        return trade_data

    except Exception as e:
        logging.error(f"World Trade Viewer Agent error: {str(e)}", exc_info=True)
        return {"error": f"World Trade Viewer failed: {str(e)}"}


def _extract_countries_from_context(context: str) -> List[str]:
    """Extract relevant country codes from scenario context"""
    if not context:
        return ["USA", "CHN", "DEU", "JPN", "GBR"]  # Default major economies

    # Simple mapping of common location names to country codes
    country_mapping = {
        "china": "CHN", "shenzhen": "CHN", "beijing": "CHN",
        "canada": "CAN", "ontario": "CAN", "toronto": "CAN",
        "mexico": "MEX", "monterrey": "MEX",
        "taiwan": "TWN", "hsinchu": "TWN",
        "korea": "KOR", "seoul": "KOR", "south korea": "KOR",
        "india": "IND", "mumbai": "IND",
        "vietnam": "VNM", "hcm city": "VNM", "ho chi minh": "VNM",
        "usa": "USA", "united states": "USA", "ohio": "USA",
        "germany": "DEU", "japan": "JPN", "uk": "GBR", "britain": "GBR"
    }

    context_lower = context.lower()
    found_countries = set(["USA"])  # Always include USA as reference

    for location, code in country_mapping.items():
        if location in context_lower:
            found_countries.add(code)

    return list(found_countries)


def _get_fallback_trade_data() -> Dict[str, Any]:
    """Provide fallback trade data when API is unavailable"""
    return {
        "timestamp": datetime.now().isoformat(),
        "data_source": "Fallback/Mock Data",
        "countries": {
            "USA": {
                "exports": {"value": "1.65T", "growth": "3.2%"},
                "imports": {"value": "2.41T", "growth": "8.1%"},
                "trade_balance": -************,
                "last_updated": "2024-Q3"
            },
            "CHN": {
                "exports": {"value": "3.36T", "growth": "0.9%"},
                "imports": {"value": "2.68T", "growth": "-0.7%"},
                "trade_balance": 680000000000,
                "last_updated": "2024-Q3"
            },
            "DEU": {
                "exports": {"value": "1.81T", "growth": "1.2%"},
                "imports": {"value": "1.72T", "growth": "2.1%"},
                "trade_balance": 90000000000,
                "last_updated": "2024-Q3"
            }
        },
        "global_trends": {
            "world_trade_growth": "2.3%",
            "merchandise_trade": "$19.5T",
            "services_trade": "$6.8T",
            "trend": "recovering"
        },
        "tariff_insights": {
            "average_tariff_rate": "7.2%",
            "recent_changes": [
                "Trade tensions affecting supply chains",
                "Regional trade agreements gaining momentum",
                "Digital trade policies evolving rapidly"
            ],
            "policy_outlook": "Cautiously optimistic with regional variations"
        },
        "analysis_context": {
            "scenario_context": "Fallback mode - API unavailable",
            "data_freshness": "Static/Historical",
            "source": "Mock Data"
        }
    }


# --- Gradio UI Definition ---
def create_ui():
    """Creates the Gradio UI with tabs, option boxes, and styling."""
    with gr.Blocks(
        title="Tariff Scenario Planner 📊",
        theme=gr.themes.Soft(primary_hue="blue"),
        css=custom_css,
    ) as demo:
        # Enhanced Header with Radiant Design
        gr.HTML("""
        <div class="header-container">
            <h1>📊 Tariff Scenario Planner</h1>
            <div class="subtitle">
                Advanced Multi-Agent Analysis for Strategic Sourcing Decisions
            </div>
        </div>
        """)

        selected_option_state = gr.State(value=None)
        full_ranking_state = gr.State(value=[])

        with gr.Tabs(elem_id="tabs_area"):
            # --- Tab 1: Recommendation ---
            with gr.TabItem("Recommendation", id=0):
                with gr.Row(variant="panel"):
                    # Input Column
                    with gr.Column(scale=1, min_width=350):
                        gr.Markdown("### 1. Select Scenario")
                        scenario_dropdown = gr.Dropdown(
                            label="Scenario",
                            choices=[
                                (f"Scenario {k} - {v.get('name', k)}", k)
                                for k, v in PREDEFINED_SCENARIOS.items()
                            ]
                            + [("Custom Scenario", "Custom")],
                            value="B",
                        )
                        
                        scenario_description = gr.HTML(
                            value="_Select a scenario to view details_",
                            elem_id="scenario_description_card"
                        )
                        
                        example_custom_json = json.dumps(
                            {
                                "options": [
                                    {
                                        "supplier": "S1",
                                        "base_cost": 100,
                                        "duty": 10,
                                        "lead_time": 20,
                                        "risk_hint": "Low",
                                    },
                                    {
                                        "supplier": "S2",
                                        "base_cost": 90,
                                        "duty": 25,
                                        "lead_time": 25,
                                        "risk_hint": 3,
                                    },
                                ]
                            },
                            indent=2,
                        )
                        
                        custom_json_input = gr.Textbox(
                            label="Custom Scenario JSON Input",
                            placeholder=f"Paste JSON here, e.g.,\n{example_custom_json}",
                            lines=10,
                            visible=False,
                            info="Input must be a JSON object with an 'options' key.",
                        )
                        
                        analyze_btn = gr.Button(
                            "🚀 Analyze Scenario", variant="primary"
                        )

                    # Output Column
                    with gr.Column(scale=2):
                        gr.Markdown("### 2. Top Options Summary")
                        with gr.Row():
                            option_1_display = gr.HTML(
                                value="_Run analysis..._",
                                elem_classes=["option_box", "rank_1"],
                            )
                        with gr.Row():
                            option_2_display = gr.HTML(
                                value="", elem_classes=["option_box", "rank_2"]
                            )
                        # Option 3 has been removed as requested

                with gr.Row():
                    with gr.Column(scale=2):
                        gr.Markdown("### 3. Decision Rationale")
                        rationale_output = gr.Markdown(
                            elem_id="rationale_md", value="_Rationale..._"
                        )
                        gr.Markdown("### 4. Strategic Considerations")
                        strategic_considerations_output = gr.Markdown(
                            value="_Considerations..._",
                            elem_id="strat_cons_md",
                        )
                    with gr.Column(scale=1, min_width=300):
                        gr.Markdown("### 5. Select & Dispatch")
                        option_radio = gr.Radio(
                            label="Select Preferred Option for Dispatch",
                            choices=[],
                            visible=False,
                            interactive=True,
                        )
                        dispatch_btn = gr.Button(
                            "✉️ Dispatch Selected Option", visible=False
                        )
                        dispatch_status = gr.Markdown(
                            value="_Select option and click dispatch..._",
                            label="Dispatch Status"
                        )

            # --- Tab 2: Detailed Ranking & Visualization ---
            with gr.TabItem("Detailed Analysis", id=1):
                gr.Markdown("### Detailed Ranking & Score Visualization")
                ranking_plot = gr.Plot(label="Overall Score Comparison")
                gr.Markdown("--- Full Ranked List ---")
                detailed_ranking_df = gr.DataFrame(
                    label="Full Ranking Data",
                    headers=[
                        "Rank",
                        "Supplier",
                        "Landed Cost",
                        "Risk",
                        "Lead Time (d)",
                        "Score",
                    ],
                    datatype=[
                        "number",
                        "str",
                        "number",
                        "number",
                        "number",
                        "number",
                    ],
                    col_count=(6, "fixed"),
                    elem_id="detailed_ranking_df",
                    value=None,
                    wrap=True,
                )

            # --- Tab 3: Input Details ---
            with gr.TabItem("Input Details", id=2):
                gr.Markdown("### Scenario Input Parameters")
                scenario_details_output = gr.Markdown(
                    "_Details..._", elem_id="scenario_details_md"
                )

            # --- Tab 4: System Context (SAP) ---
            with gr.TabItem("SAP Data Context", id=3):
                gr.Markdown("### SAP Enterprise Data Context")
                erp_context_output = gr.Markdown(
                    "_Loading SAP data..._", elem_id="erp_context_md"
                )
                refresh_sap_btn = gr.Button("🔄 Refresh SAP Data")

            # --- Tab 5: Agent Discussion ---
            with gr.TabItem("Agent Discussion", id=4):
                gr.Markdown("### Simulated Agent Workflow Log")
                discussion_log_output = gr.Markdown(
                    value="_Agent log..._", elem_id="discussion_log_md"
                )

            # --- Tab 6: World Trade Data ---
            with gr.TabItem("🌍 World Trade Data", id=5):
                gr.Markdown("### Real-Time World Trade Statistics (WTO API)")
                with gr.Row():
                    with gr.Column(scale=1):
                        trade_refresh_btn = gr.Button("🔄 Refresh Trade Data", variant="primary")
                        trade_status = gr.Markdown("_Click refresh to load trade data..._")
                    with gr.Column(scale=2):
                        trade_data_output = gr.Markdown("_No data loaded yet..._", elem_id="trade_data_md")

            # --- Tab 7: Global Trade News ---
            with gr.TabItem("🌐 Global Trade News", id=6):
                gr.Markdown(
                    "### Latest News on International Trade & Tariffs (via Currents API)"
                )
                with gr.Row():
                    news_query_input = gr.Textbox(
                        label="News Search Query (Optional)",
                        value="international trade tariffs OR global supply chain news OR trade policy",
                    )
                    fetch_news_btn = gr.Button("📰 Fetch Latest News")
                news_output_md = gr.Markdown(
                    "_Click button..._", elem_id="news_output_md"
                )

            # --- Tab 8: System Diagram ---
            with gr.TabItem("System Diagram", id=7):
                gr.Markdown("### Agent Workflow Diagram")
                diagram_btn = gr.Button("🔄 Generate Diagram")
                diagram_html_output = gr.HTML(
                    label="System Flow Diagram", elem_id="diagram_display"
                )

        # --- UI Interaction Logic ---
        def format_option_md(option_data):
            if not option_data or not isinstance(option_data, dict):
                return "_N/A_"
            rank = option_data.get("rank", "N/A")
            supplier = option_data.get("supplier", "N/A")
            summary = option_data.get("summary", "No summary")
            details = option_data.get("details", {})
            score = details.get("score", 0)
            cost = details.get("landed_cost", 0)
            risk = details.get("risk", 0)
            lt = details.get("lead_time", 0)
            md = f"""<div class='option_box rank_{rank}'>
<h4>Option {rank}: {supplier}</h4>
<span class='summary'>{summary}</span>
<div class='details'>
<strong>Score:</strong> {score:.4f}<br>
<strong>Landed Cost:</strong> ${cost:,.2f}<br>
<strong>Risk Score:</strong> {risk} (1=Low, 5=High)<br>
<strong>Lead Time:</strong> {lt} days
</div>
</div>"""
            return md

        def get_scenario_details(scenario_id, custom_json_input=None):
            """Formats the details of a selected scenario for display."""
            if (scenario_id == "Custom"):
                if not custom_json_input:
                    return "_No custom scenario details provided._"
                try:
                    custom_data = json.loads(custom_json_input)
                    options = custom_data.get("options", [])
                    if not options:
                        return "_Custom JSON has no options array._"
                    
                    md = "### Custom Scenario Details\n\n"
                    md += "**Format:** User-provided JSON\n\n"
                    md += "**Options:**\n\n"
                    md += "|Supplier|Base Cost|Duty %|Lead Time|Risk Hint|\n"
                    md += "|---------|----------|------|---------|----------|\n"
                    
                    for opt in options:
                        supplier = opt.get("supplier", "N/A")
                        base_cost = opt.get("base_cost", "N/A")
                        duty = opt.get("duty", "N/A")
                        lead_time = opt.get("lead_time", "N/A")
                        risk_hint = opt.get("risk_hint", "N/A")
                        md += f"|{supplier}|${base_cost}|{duty}%|{lead_time} days|{risk_hint}|\n"
                    
                    return md
                except json.JSONDecodeError:
                    return "_Invalid custom JSON format._"
                except Exception as e:
                    return f"_Error parsing custom data: {str(e)}_"
            
            # Standard predefined scenario
            scenario = PREDEFINED_SCENARIOS.get(scenario_id)
            if not scenario:
                return f"_Scenario '{scenario_id}' not found._"
            
            name = scenario.get("name", f"Scenario {scenario_id}")
            product = scenario.get("product", "N/A")
            hs_code = scenario.get("hs_code", "N/A")
            description = scenario.get("description", "No description available.")
            options = scenario.get("options", [])
            
            md = f"### {name}\n\n"
            md += f"**Product:** {product}\n\n"
            md += f"**HS Code:** {hs_code}\n\n"
            md += f"**Description:** {description}\n\n"
            md += "**Options:**\n\n"
            md += "|Supplier|Base Cost|Duty %|Lead Time|Risk Hint|\n"
            md += "|---------|----------|------|---------|----------|\n"
            
            for opt in options:
                supplier = opt.get("supplier", "N/A")
                base_cost = opt.get("base_cost", "N/A")
                duty = opt.get("duty", "N/A")
                lead_time = opt.get("lead_time", "N/A")
                risk_hint = opt.get("risk_hint", "N/A")
                md += f"|{supplier}|${base_cost}|{duty}%|{lead_time} days|{risk_hint}|\n"
            
            return md

        def fetch_sap_data(scenario_id):
            """Fetches SAP data via OData for the scenario context."""
            # Create the SAP connector
            sap_connector = SAPODataConnector(verify_ssl=False)
            
            # Test connection and fetch data if successful
            if (sap_connector.test_connection()):
                context_data = sap_connector.get_sap_context_data()
                if context_data:
                    return context_data
            
            # If we couldn't get real data, return fallback data
            logging.warning("Using fallback SAP data because real connection failed")
            current_date = datetime.now().strftime("%Y-%m-%d")
            
            # Fallback data with connection error indicators
            fallback_data = {
                "transaction_date": current_date,
                "system_source": "SAP S/4HANA (Connection Failed - Using Mock Data)",
                "connection_status": "Failed",
                "procurement_context": {
                    "annual_spend": "$1.2M to $1.8M (Est.)",
                    "active_suppliers": "Unknown",
                    "order_count": "Unknown",
                },
                "trade_context": {
                    "currencies": ["USD", "EUR"],
                    "revenue_by_currency": {"USD": "$800,000.00 (Est.)", "EUR": "$400,000.00 (Est.)"},
                    "top_markets": ["Unknown"]
                },
                "supply_chain_kpis": {
                    "products_count": "Unknown",
                    "avg_product_price": "Unknown",
                    "avg_order_value": "Unknown",
                    "line_items_count": "Unknown"
                },
                "sample_orders": []
            }
            
            return fallback_data

        def format_erp_display(erp_data):
            """Formats SAP data for display in the UI."""
            # Get connection status to determine styling
            connection_status = erp_data.get("connection_status", "Unknown")
            status_color = "#4caf50" if connection_status == "Connected" else "#f44336"
            
            md = "<h4>SAP System Context</h4>\n\n"
            
            # Show connection status prominently
            md += f"<div style='padding: 10px; background-color: {status_color}20; border-left: 4px solid {status_color}; margin-bottom: 15px;'>"
            md += f"<strong>Connection Status:</strong> {connection_status}"
            md += "</div>\n\n"
            
            procurement = erp_data.get("procurement_context", {})
            trade = erp_data.get("trade_context", {})
            kpis = erp_data.get("supply_chain_kpis", {})
            
            md += "<ul>\n"
            md += f"<li><strong>Date:</strong> {erp_data.get('transaction_date', 'N/A')}</li>\n"
            md += f"<li><strong>Source:</strong> <em>{erp_data.get('system_source', 'SAP')}</em></li>\n"
            md += "</ul>\n\n"
            
            md += "<h4>Procurement Context</h4>\n"
            md += "<ul>\n"
            for key, value in procurement.items():
                formatted_key = key.replace("_", " ").title()
                md += f"<li><strong>{formatted_key}:</strong> {value}</li>\n"
            md += "</ul>\n\n"
            
            md += "<h4>Trade Context</h4>\n"
            md += "<ul>\n"
            currencies = trade.get("currencies", [])
            if currencies:
                md += f"<li><strong>Active Currencies:</strong> {', '.join(currencies)}</li>\n"
            
            revenue_by_currency = trade.get("revenue_by_currency", {})
            if revenue_by_currency:
                md += "<li><strong>Revenue By Currency:</strong><ul>\n"
                for currency, amount in revenue_by_currency.items():
                    md += f"<li>{currency}: {amount}</li>\n"
                md += "</ul></li>\n"
            
            top_markets = trade.get("top_markets", [])
            if top_markets:
                md += f"<li><strong>Top Markets:</strong> {', '.join(top_markets)}</li>\n"
            md += "</ul>\n\n"
            
            md += "<h4>Supply Chain KPIs</h4>\n"
            md += "<ul>\n"
            for key, value in kpis.items():
                formatted_key = key.replace("_", " ").title()
                md += f"<li><strong>{formatted_key}:</strong> {value}</li>\n"
            md += "</ul>\n"
            
            # Add sample orders table if available
            sample_orders = erp_data.get("sample_orders", [])
            if sample_orders:
                md += "<h4>Recent Orders</h4>\n"
                md += "<table style='width:100%; border-collapse:collapse; font-size:0.9em;'>\n"
                md += "<tr style='background-color:#f2f2f2;'>"
                md += "<th style='padding:8px; text-align:left; border:1px solid #ddd;'>Order ID</th>"
                md += "<th style='padding:8px; text-align:left; border:1px solid #ddd;'>Customer</th>"
                md += "<th style='padding:8px; text-align:left; border:1px solid #ddd;'>Amount</th>"
                md += "<th style='padding:8px; text-align:left; border:1px solid #ddd;'>Currency</th>"
                md += "<th style='padding:8px; text-align:left; border:1px solid #ddd;'>Date</th>"
                md += "</tr>\n"
                
                for order in sample_orders:
                    md += "<tr style='border:1px solid #ddd;'>"
                    md += f"<td style='padding:8px; text-align:left; border:1px solid #ddd;'>{order.get('SalesOrderID', 'N/A')}</td>"
                    md += f"<td style='padding:8px; text-align:left; border:1px solid #ddd;'>{order.get('CustomerID', 'N/A')}</td>"
                    md += f"<td style='padding:8px; text-align:left; border:1px solid #ddd;'>{float(order.get('GrossAmount', 0)):,.2f}</td>"
                    md += f"<td style='padding:8px; text-align:left; border:1px solid #ddd;'>{order.get('CurrencyCode', 'N/A')}</td>"
                    md += f"<td style='padding:8px; text-align:left; border:1px solid #ddd;'>{order.get('CreationDate', 'N/A')}</td>"
                    md += "</tr>\n"
                
                md += "</table>\n"
            
            return md

        def format_trade_data_display(trade_data):
            """Formats world trade data for display in the UI."""
            if not trade_data or isinstance(trade_data, str):
                return "_No trade data available or error occurred._"

            # Check for error in trade data
            if "error" in trade_data:
                return f"**Error:** {trade_data['error']}"

            md = "### 🌍 World Trade Statistics\n\n"

            # Add timestamp and source info
            timestamp = trade_data.get("timestamp", "Unknown")
            source = trade_data.get("analysis_context", {}).get("source", "Unknown")
            data_freshness = trade_data.get("analysis_context", {}).get("data_freshness", "Unknown")

            md += f"**Last Updated:** {timestamp[:19] if timestamp else 'Unknown'}\n\n"
            md += f"**Data Source:** {source} ({data_freshness})\n\n"

            # Global trends section
            global_trends = trade_data.get("global_trends", {})
            if global_trends:
                md += "#### 📈 Global Trade Trends\n\n"
                md += f"- **World Trade Growth:** {global_trends.get('world_trade_growth', 'N/A')}\n"
                md += f"- **Merchandise Trade:** {global_trends.get('merchandise_trade', 'N/A')}\n"
                md += f"- **Services Trade:** {global_trends.get('services_trade', 'N/A')}\n"
                md += f"- **Overall Trend:** {global_trends.get('trend', 'N/A')}\n\n"

            # Country-specific data
            countries = trade_data.get("countries", {})
            if countries:
                md += "#### 🌎 Country Trade Data\n\n"
                for country_code, country_data in countries.items():
                    md += f"**{country_code}:**\n"

                    exports = country_data.get("exports", {})
                    imports = country_data.get("imports", {})
                    trade_balance = country_data.get("trade_balance", 0)

                    if exports:
                        md += f"- Exports: {exports.get('value', 'N/A')}"
                        if exports.get('growth'):
                            md += f" (Growth: {exports.get('growth')})"
                        md += "\n"

                    if imports:
                        md += f"- Imports: {imports.get('value', 'N/A')}"
                        if imports.get('growth'):
                            md += f" (Growth: {imports.get('growth')})"
                        md += "\n"

                    if trade_balance:
                        balance_str = f"${trade_balance/1e9:.1f}B" if abs(trade_balance) >= 1e9 else f"${trade_balance/1e6:.1f}M"
                        balance_indicator = "surplus" if trade_balance > 0 else "deficit"
                        md += f"- Trade Balance: {balance_str} ({balance_indicator})\n"

                    md += "\n"

            # Tariff insights section
            tariff_insights = trade_data.get("tariff_insights", {})
            if tariff_insights:
                md += "#### 📊 Tariff Insights\n\n"
                md += f"**Average Tariff Rate:** {tariff_insights.get('average_tariff_rate', 'N/A')}\n\n"

                recent_changes = tariff_insights.get("recent_changes", [])
                if recent_changes:
                    md += "**Recent Policy Changes:**\n"
                    for change in recent_changes:
                        md += f"- {change}\n"
                    md += "\n"

                policy_outlook = tariff_insights.get("policy_outlook", "")
                if policy_outlook:
                    md += f"**Policy Outlook:** {policy_outlook}\n\n"

            return md

        def fetch_trade_data():
            """Fetches world trade data using the agent function."""
            try:
                # Call the agent function to get trade data
                trade_data = agent_world_trade_viewer()

                if isinstance(trade_data, dict) and "error" not in trade_data:
                    formatted_data = format_trade_data_display(trade_data)
                    status = "✅ Trade data loaded successfully"
                else:
                    formatted_data = format_trade_data_display(trade_data)
                    status = "⚠️ Trade data loaded with warnings"

                return status, formatted_data

            except Exception as e:
                logging.error(f"Error fetching trade data: {str(e)}", exc_info=True)
                error_msg = f"❌ Error fetching trade data: {str(e)}"
                return error_msg, "_Failed to load trade data._"

        def generate_ranking_plot(ranking_data):
            """Generates a bar plot for the option rankings."""
            if not ranking_data:
                fig = go.Figure()
                fig.update_layout(
                    title="No ranking data available",
                    xaxis_title="Options",
                    yaxis_title="Score",
                )
                return fig
            
            df = pd.DataFrame(ranking_data)
            if df.empty or "supplier" not in df.columns or "score" not in df.columns:
                fig = go.Figure()
                fig.update_layout(
                    title="Incomplete ranking data",
                    xaxis_title="Options",
                    yaxis_title="Score",
                )
                return fig
                
            # Create a color scale based on rank
            colors = ["#10B981", "#F59E0B", "#6B7280", "#9CA3AF", "#D1D5DB"]
            color_map = {
                i+1: colors[min(i, len(colors)-1)] 
                for i in range(len(df))
            }
            
            labels = [f"Option {row['rank']}: {row['supplier']}" for _, row in df.iterrows()]
            
            fig = px.bar(
                df, 
                x="supplier", 
                y="score",
                labels={"supplier": "Option", "score": "Overall Score"},
                color="rank",
                color_discrete_map=color_map,
                text="score"
            )
            
            fig.update_traces(
                texttemplate='%{text:.4f}', 
                textposition='auto',
                hovertemplate="<b>%{x}</b><br>Score: %{y:.4f}<extra></extra>"
            )
            
            fig.update_layout(
                title="Option Score Comparison",
                xaxis_title="Options",
                yaxis_title="Overall Score (higher is better)",
                legend_title="Rank",
                plot_bgcolor="white",
                font=dict(size=12),
                height=500,
            )
            
            return fig

        def generate_system_diagram():
            """Generates an SVG diagram of the system workflow."""
            try:
                # Check if graphviz is available
                if not shutil.which("dot"):
                    return "<div class='graphviz-error'>Graphviz not installed. Cannot generate diagram.</div>"
                
                dot = Digraph(comment="Tariff Scenario Analysis Workflow")
                
                # Set diagram properties
                dot.attr('graph', 
                    rankdir='TB', 
                    bgcolor='white',
                    fontname='Arial',
                    pad='0.5',
                    nodesep='0.8',
                    ranksep='0.8'
                )
                
                dot.attr('node', 
                    shape='box', 
                    style='rounded,filled', 
                    fillcolor='#E5F6F2', 
                    color='#0F766E',
                    fontname='Arial',
                    fontsize='12',
                    margin='0.2,0.1'
                )
                
                dot.attr('edge', 
                    color='#64748B', 
                    fontname='Arial',
                    fontsize='10',
                    fontcolor='#64748B'
                )
                
                # Add nodes
                dot.node('input', 'Scenario Selection &\nCustom Input', fillcolor='#DBEAFE', color='#2563EB')
                dot.node('sap', 'SAP OData\nConnection', fillcolor='#FEF3C7', color='#D97706')
                dot.node('wto', 'WTO API\n(World Trade Data)', fillcolor='#F0FDF4', color='#166534')
                dot.node('data_loader', 'Data Loader Agent')
                dot.node('cost_calc', 'Cost Calculator Agent')
                dot.node('risk_assess', 'Risk Assessor Agent')
                dot.node('strategy', 'Strategy Determiner Agent')
                dot.node('ui', 'UI Dashboard', fillcolor='#FEF3C7', color='#D97706')
                dot.node('news', 'News API (Currents)', fillcolor='#F3F4F6', color='#6B7280')
                dot.node('email', 'Email Dispatch (Logic App)', fillcolor='#F3F4F6', color='#6B7280')
                
                # Add edges
                dot.edge('input', 'data_loader', 'User selects scenario')
                dot.edge('sap', 'ui', 'Enterprise context')
                dot.edge('wto', 'ui', 'Global trade data')
                dot.edge('data_loader', 'cost_calc', 'Options data')
                dot.edge('cost_calc', 'risk_assess', 'Options with costs')
                dot.edge('risk_assess', 'strategy', 'Options with risks')
                dot.edge('strategy', 'ui', 'Analysis results')
                dot.edge('news', 'ui', 'Trade news (optional)')
                dot.edge('ui', 'email', 'User selects option to dispatch')
                
                # Add invisible edges to enforce layout
                dot.edge('input', 'news', style='invis')
                
                # Render the graph
                svg_content = dot.pipe(format='svg').decode('utf-8')
                
                # Post-process SVG to add responsive behavior
                svg_content = svg_content.replace('<svg ', '<svg style="max-width:100%; height:auto;" ')
                
                return svg_content
                
            except Exception as e:
                logging.error(f"Error generating diagram: {e}", exc_info=True)
                return f"<div class='graphviz-error'>Error generating diagram: {str(e)}</div>"

        def handle_analysis_click(scenario_choice, custom_json_input):
            """Handles the Analyze button click event."""
            # Run the analysis
            analysis_result, discussion_log = run_scenario_analysis(
                scenario_choice, custom_json_input
            )

            # Extract top options
            top_options = analysis_result.get("top_options", [])
            option_1 = (
                format_option_md(top_options[0])
                if len(top_options) > 0
                else "_No options available._"
            )
            option_2 = (
                format_option_md(top_options[1])
                if len(top_options) > 1
                else ""
            )
            rationale = analysis_result.get("decision_rationale", "_No rationale available._")
            considerations = analysis_result.get("strategic_considerations", ["_No considerations available._"])

            # Format considerations for Markdown, handling icons within the list items
            considerations_md = ""
            if considerations:
                considerations_md += "<ul>\n"
                for item in considerations:
                    icon = "➡️" # Default icon
                    escaped_item = html.escape(item)
                    # Check for specific icons and adjust display
                    if "⚠️" in item: icon = "⚠️"; escaped_item = html.escape(item.replace("⚠️", "").strip())
                    elif "⚖️" in item: icon = "⚖️"; escaped_item = html.escape(item.replace("⚖️", "").strip())
                    elif "💰" in item: icon = "💰"; escaped_item = html.escape(item.replace("💰", "").strip())
                    elif "📈" in item: icon = "📈"; escaped_item = html.escape(item.replace("📈", "").strip())
                    elif "✅" in item: icon = "✅"; escaped_item = html.escape(item.replace("✅", "").strip())
                    # Use data-icon attribute for CSS styling
                    considerations_md += f"<li data-icon='{icon} '>{escaped_item}</li>\n"
                considerations_md += "</ul>"
            else:
                considerations_md = "_No specific considerations flagged by the model._"

            # Generate ranking plot
            ranking_plot = generate_ranking_plot(
                analysis_result.get("full_ranking", [])
            )

            # Prepare detailed ranking dataframe
            detailed_ranking = analysis_result.get("full_ranking", [])
            detailed_ranking_df_val = pd.DataFrame(detailed_ranking) if detailed_ranking else pd.DataFrame()

            # Get scenario details
            scenario_details = get_scenario_details(
                scenario_choice, custom_json_input
            )

            # Get ERP context
            erp_data = fetch_sap_data(scenario_choice)
            erp_context = format_erp_display(erp_data)

            # Generate system diagram
            diagram_html = generate_system_diagram()

            # Prepare radio choices for dispatch
            radio_choices = []
            is_valid_analysis = False
            if top_options and isinstance(top_options, list):
                 # Check if analysis actually produced valid options
                 is_valid_analysis = any(opt and isinstance(opt, dict) and 'rank' in opt and 'supplier' in opt for opt in top_options)
                 if is_valid_analysis:
                     for option in top_options:
                         if option and isinstance(option, dict):
                             supplier = option.get("supplier", "Unknown")
                             rank = option.get("rank", "?")
                             # Ensure unique choices if suppliers have same name
                             radio_choices.append(f"Option {rank}: {supplier}")
                     logging.info(f"Generated radio choices: {radio_choices}")
                 else:
                     logging.warning("Analysis finished, but no valid top options found for dispatch choices.")
            else:
                logging.warning("Analysis finished, but 'top_options' is missing or not a list.")

            # Determine visibility of dispatch controls
            show_dispatch_controls = is_valid_analysis and bool(radio_choices)

            return (
                option_1, option_2, # Option displays
                rationale, considerations_md, # Rationale & Considerations
                ranking_plot, detailed_ranking_df_val, # Plot & DF
                scenario_details, erp_context, discussion_log, # Details, Context, Log
                "_Click 'Fetch Latest News' to get trade news._", # News placeholder
                diagram_html, # Diagram
                gr.update(choices=radio_choices, visible=show_dispatch_controls, value=None), # Update radio
                gr.update(visible=show_dispatch_controls), # Update dispatch button visibility
                initial_dispatch_status, # Reset dispatch status display
            )

        def handle_dispatch_click(selected_option):
            """Handles the Dispatch button click event. Returns result for Markdown."""
            if not selected_option:
                # Use gr.Warning which should render in Markdown
                gr.Warning("No Selection: Please select an option first.")
                # Return a string that Markdown can display as well
                return "<p style='color: orange;'>⚠️ Please select an option first.</p>"

            # Call the dispatch function which now includes gr.Warning/Error calls
            dispatch_result_message = dispatch_results_email(selected_option)

            # The dispatch function handles gr.Warning/Error,
            # so we just return the message string for the Markdown component.
            # We can optionally format it slightly more nicely here.
            if dispatch_result_message.startswith("✔"):
                return f"<p style='color: green;'>{dispatch_result_message}</p>"
            elif dispatch_result_message.startswith("✖"):
                 # Errors/Warnings are already shown via gr.Warning/Error
                 # Still return the message to the box for record
                 return f"<p style='color: red;'>{dispatch_result_message}</p>"
            else:
                 # Fallback for unexpected message format
                 return dispatch_result_message

        def handle_news_click(query):
            """Handles the Fetch News button click event."""
            return fetch_trade_news(query)

        def handle_diagram_click():
            """Handles the Generate Diagram button click event."""
            return generate_system_diagram()

        def toggle_custom_input(scenario_choice):
            return gr.update(visible=(scenario_choice == "Custom"))

        def update_scenario_description(scenario_choice):
            """Updates the scenario description markdown when a scenario is selected."""
            if scenario_choice == "Custom":
                return "### Custom Scenario\n\nEnter your custom scenario details in the JSON input box below."
            
            scenario = PREDEFINED_SCENARIOS.get(scenario_choice)
            if not scenario:
                return "_Invalid scenario selection._"
            
            name = scenario.get("name", f"Scenario {scenario_choice}")
            product = scenario.get("product", "N/A")
            hs_code = scenario.get("hs_code", "N/A")
            description = scenario.get("description", "No description available.")
            
            # Create a colorful description card with enhanced styling
            html_content = f"""
            <div class="scenario-card scenario-{scenario_choice.lower()}">
                <h3>{name}</h3>
                <div class="scenario-details">
                    <div class="scenario-product"><span>Product:</span> {product}</div>
                    <div class="scenario-hs-code"><span>HS Code:</span> {hs_code}</div>
                </div>
                <div class="scenario-description">{description}</div>
            </div>
            """
            
            return html_content

        def refresh_sap_data(scenario_choice):
            """Handler for refreshing SAP data via button click"""
            sap_data = fetch_sap_data(scenario_choice)
            sap_context = format_erp_display(sap_data)
            return sap_context

        # --- Connect UI Events ---
        analyze_btn.click(
            fn=handle_analysis_click,
            inputs=[scenario_dropdown, custom_json_input],
            outputs=[
                option_1_display,
                option_2_display,
                rationale_output,
                strategic_considerations_output,
                ranking_plot,
                detailed_ranking_df,
                scenario_details_output,
                erp_context_output,
                discussion_log_output,
                news_output_md,
                diagram_html_output,
                option_radio,
                dispatch_btn,
                dispatch_status,
            ],
        )

        dispatch_btn.click(
            fn=handle_dispatch_click,
            inputs=[option_radio],
            outputs=[dispatch_status],
        )

        fetch_news_btn.click(
            fn=handle_news_click,
            inputs=[news_query_input],
            outputs=[news_output_md],
        )

        diagram_btn.click(
            fn=handle_diagram_click, inputs=[], outputs=[diagram_html_output]
        )
        
        scenario_dropdown.change(
            fn=toggle_custom_input,
            inputs=[scenario_dropdown],
            outputs=[custom_json_input]
        )

        scenario_dropdown.change(
            fn=update_scenario_description,
            inputs=[scenario_dropdown],
            outputs=[scenario_description]
        )

        refresh_sap_btn.click(
            fn=refresh_sap_data,
            inputs=[scenario_dropdown],
            outputs=[erp_context_output]
        )

        trade_refresh_btn.click(
            fn=fetch_trade_data,
            inputs=[],
            outputs=[trade_status, trade_data_output]
        )

        return demo


# --- Enhanced Custom CSS Styling ---
custom_css = """
/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap');

/* Global Styles with Enhanced Design */
body, .gradio-container {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    font-size: 15px;
    line-height: 1.6;
    color: #1a202c;
}

.gradio-container {
    max-width: 1400px;
    margin: auto;
    padding: 20px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Enhanced Header with Radiant Design */
.header-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    padding: 30px 40px;
    margin: -20px -20px 30px -20px;
    border-radius: 20px 20px 0 0;
    position: relative;
    overflow: hidden;
}

.header-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 50%, rgba(255,255,255,0.1) 100%);
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { transform: translateX(-100%); }
    50% { transform: translateX(100%); }
}

h1 {
    color: #ffffff;
    text-align: center;
    margin: 0;
    font-size: 2.8rem;
    font-weight: 700;
    text-shadow: 0 4px 8px rgba(0,0,0,0.3);
    letter-spacing: -0.02em;
    position: relative;
    z-index: 1;
}

.subtitle {
    color: rgba(255, 255, 255, 0.9);
    text-align: center;
    font-size: 1.1rem;
    font-weight: 400;
    margin-top: 8px;
    position: relative;
    z-index: 1;
}

/* Enhanced Tab System */
#tabs_area {
    border: none;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    background: #ffffff;
}

.tab-nav {
    background: linear-gradient(90deg, #f8fafc 0%, #e2e8f0 100%);
    border-bottom: 1px solid #e2e8f0;
    padding: 0 20px;
}

.tab-nav button {
    font-size: 1rem;
    padding: 16px 28px;
    border: none !important;
    border-radius: 12px 12px 0 0 !important;
    margin: 8px 4px 0 4px;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: transparent;
    color: #64748b;
    position: relative;
}

.tab-nav button:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    transform: translateY(-2px);
}

.tab-nav button.selected {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: #ffffff !important;
    font-weight: 600;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.tabitem {
    padding: 30px 40px;
    background: #ffffff;
    min-height: 600px;
}

/* Enhanced Typography */
h3 {
    color: #1e293b;
    margin-top: 2rem;
    margin-bottom: 1rem;
    font-size: 1.4rem;
    font-weight: 600;
    position: relative;
    padding-left: 20px;
}

h3::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 24px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
}

/* Enhanced Buttons */
.gr-button {
    margin-top: 16px;
    border-radius: 12px !important;
    padding: 14px 28px !important;
    font-size: 1rem !important;
    font-weight: 600 !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    border: none !important;
    cursor: pointer !important;
}

button.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4) !important;
}

button.primary:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.6) !important;
}

.gr-input-label > span, .gr-output-label > span {
    font-weight: 600;
    color: #374151;
    font-size: 1rem;
    margin-bottom: 8px;
}

/* Enhanced Option Boxes */
.option_box {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid #e2e8f0;
    border-radius: 16px;
    padding: 24px 28px;
    margin-bottom: 20px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    min-height: 180px;
    position: relative;
    overflow: hidden;
}

.option_box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 6px;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 0 16px 16px 0;
}

.option_box:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 24px rgba(0,0,0,0.15);
}

.option_box.rank_1::before {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.option_box.rank_2::before {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.option_box.rank_3::before {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
}

.option_box h4 {
    margin: 0 0 12px 0;
    font-size: 1.25rem;
    font-weight: 700;
    color: #1e293b;
    letter-spacing: -0.01em;
}

.option_box .summary {
    font-weight: 600;
    color: #667eea;
    margin-bottom: 16px;
    display: inline-block;
    font-size: 1rem;
    background: rgba(102, 126, 234, 0.1);
    padding: 8px 16px;
    border-radius: 20px;
    border: 1px solid rgba(102, 126, 234, 0.2);
}

.option_box .details {
    font-size: 0.95rem;
    color: #64748b;
    line-height: 1.7;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
}

.option_box .details strong {
    color: #334155;
    font-weight: 600;
    display: block;
    margin-bottom: 4px;
}

/* Enhanced Rationale and Considerations */
#rationale_md > p, #strat_cons_md > ul > li {
    font-size: 1rem;
    line-height: 1.7;
    color: #374151;
}

#rationale_md > p {
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
    padding: 24px;
    border-radius: 12px;
    border-left: 4px solid #667eea;
    margin: 0 0 20px 0;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

#strat_cons_md {
    background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
    border: 1px solid #fde68a;
    color: #92400e;
    border-radius: 12px;
    padding: 20px 24px;
    margin-top: 20px;
    box-shadow: 0 2px 8px rgba(245, 158, 11, 0.1);
}

#strat_cons_md ul {
    list-style: none;
    padding-left: 0;
    margin: 0;
}

#strat_cons_md li {
    margin-bottom: 12px;
    padding-left: 2em;
    position: relative;
    font-size: 1rem;
    line-height: 1.6;
}

#strat_cons_md li::before {
    content: attr(data-icon);
    position: absolute;
    left: 0;
    top: 2px;
    font-size: 1.2em;
    color: #d97706;
}

#strat_cons_md strong {
    color: #92400e;
    font-weight: 700;
}

/* Detailed Ranking Tab */
#detailed_ranking_df { margin-top: 15px; }
#detailed_ranking_df table { width: 100%; border-collapse: collapse; }
/* Input Details Tab */
#scenario_details_md { background: #F9FAFB; padding: 20px 25px; border-radius: 8px; border: 1px solid #E5E7EB; margin-top: 15px; }
#scenario_details_md h3 { margin-top: 0; color: #0F766E; border-bottom: 1px solid #D1D5DB; padding-bottom: 8px; margin-bottom: 15px; font-size: 1.4rem;}
#scenario_details_md p { margin-bottom: 18px; line-height: 1.7; color: #4B5563; font-size: 1.05rem;}
#scenario_details_md table { width: 100%; border-collapse: collapse; margin-top: 15px; display: block; overflow-x: auto; white-space: nowrap; }
#scenario_details_md th, #scenario_details_md td { text-align: left; padding: 10px 14px; border: 1px solid #E5E7EB; font-size: 0.95rem; }
#scenario_details_md th { background-color: #E5E7EB; font-weight: 600; color: #1F2937; }

/* System Context Tab */
#erp_context_md { background: #FFF7ED; padding: 15px 20px; border-radius: 8px; border-left: 5px solid #FB923C; margin-top: 15px; color: #7C2D12; }
#erp_context_md h4 { color: #C2410C; margin-top: 0; border-bottom: 1px dashed #FED7AA; padding-bottom: 5px; margin-bottom: 10px;}
#erp_context_md ul { list-style: none; padding-left: 0; }
#erp_context_md li { margin-bottom: 5px; }
#erp_context_md strong { color: #9A3412; }
#erp_context_md em { color: #B45309; font-size: 0.9em; }

/* Agent Discussion Log Styling */
#discussion_log_md { background: #F1F5F9; border: 1px solid #CBD5E1; border-radius: 5px; padding: 15px 20px; max-height: 600px; overflow-y: auto; font-size: 0.9rem; line-height: 1.6; color: #334155; }
#discussion_log_md strong { color: #1E3A8A; } /* Darker Blue */
#discussion_log_md span[style*='color:red'] { font-weight: bold; color: #DC2626 !important; } /* Darker Red */
#discussion_log_md > p { margin-bottom: 0.7em; border-bottom: 1px dotted #CBD5E1; padding-bottom: 0.5em;}
#discussion_log_md > p:last-child { border-bottom: none; }

/* World Trade Data Tab Styling */
#trade_data_md {
    background: #F0FDF4;
    border: 1px solid #BBF7D0;
    border-radius: 8px;
    padding: 20px;
    max-height: 600px;
    overflow-y: auto;
    font-size: 0.95rem;
    line-height: 1.6;
    margin-top: 10px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}
#trade_data_md h3, #trade_data_md h4 {
    margin-top: 0;
    color: #166534;
    border-bottom: 1px solid #BBF7D0;
    padding-bottom: 8px;
    margin-bottom: 15px;
    font-weight: 600;
}
#trade_data_md strong { color: #15803D; }
#trade_data_md ul { list-style: none; padding-left: 0; }
#trade_data_md li { margin-bottom: 8px; padding-left: 1em; position: relative; }
#trade_data_md li:before {
    content: "🌍";
    position: absolute;
    left: 0;
    top: 0;
}

/* News Feed Styling (More Vibrant) */
#news_output_md { background: #EFF6FF; border: 1px solid #BFDBFE; border-radius: 8px; padding: 20px; max-height: 600px; overflow-y: auto; font-size: 0.95rem; line-height: 1.6; margin-top: 10px; box-shadow: 0 1px 3px rgba(0,0,0,0.05); }
#news_output_md h3 { margin-top: 0; color: #1E4ED8; border-bottom: 1px solid #93C5FD; padding-bottom: 8px; margin-bottom: 15px; font-weight: 600; }
#news_output_md p > strong > a { color: #2563EB; text-decoration: none; font-weight: 600; }
#news_output_md p > strong > a:hover { text-decoration: underline; color: #1D4ED8; }
#news_output_md > blockquote { border-left: 4px solid #60A5FA; margin-left: 0; padding-left: 1.2em; color: #374151; font-style: italic; background-color: #DBEAFE; padding: 8px 12px; border-radius: 4px; }
#news_output_md > blockquote > small { color: #6B7280; font-style: normal; display: block; margin-top: 6px; font-size: 0.85em;}
#news_output_md hr { border: none; border-top: 1px dashed #BFDBFE; margin: 20px 0; }

/* System Diagram */
#diagram_display svg { 
  width: 100%; 
  height: auto; 
  display: block; 
  background: #FFF; 
  border-radius: 8px; 
  padding: 15px; 
  box-shadow: 0 2px 5px rgba(0,0,0,0.1); 
  border: 1px solid #E5E7EB; 
}

/* Scenario Description Card Styling */
#scenario_description_card {
  margin-top: 15px;
  margin-bottom: 15px;
}

.scenario-card {
  background: linear-gradient(to right, rgba(255,255,255,0.9), rgba(255,255,255,0.7));
  border-radius: 10px;
  padding: 15px;
  box-shadow: 0 4px 6px rgba(0,0,0,0.08);
  margin-bottom: 15px;
  position: relative;
  overflow: hidden;
}

.scenario-card h3 {
  margin-top: 0;
  color: #0F766E;
  font-size: 1.3rem;
  border-bottom: 2px solid;
  display: inline-block;
  padding-bottom: 5px;
  margin-bottom: 12px;
}

.scenario-details {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 12px;
  font-size: 0.9rem;
}

.scenario-details > div {
  margin-right: 20px;
  margin-bottom: 8px;
}

.scenario-details span {
  font-weight: bold;
  color: #0F766E;
}

.scenario-description {
  background-color: rgba(255,255,255,0.8);
  padding: 10px;
  border-radius: 5px;
  border-left: 3px solid;
  font-size: 0.95rem;
  line-height: 1.5;
}

/* Scenario-specific styling */
.scenario-a {
  background: linear-gradient(135deg, #ECFDF5 0%, #D1FAE5 100%);
  border: 1px solid #A7F3D0;
}
.scenario-a h3 { border-color: #10B981; }
.scenario-a .scenario-description { border-color: #10B981; }

.scenario-b {
  background: linear-gradient(135deg, #EFF6FF 0%, #DBEAFE 100%);
  border: 1px solid #BFDBFE;
}
.scenario-b h3 { border-color: #3B82F6; }
.scenario-b .scenario-description { border-color: #3B82F6; }

.scenario-c {
  background: linear-gradient(135deg, #FFFBEB 0%, #FEF3C7 100%);
  border: 1px solid #FDE68A;
}
.scenario-c h3 { border-color: #F59E0B; }
.scenario-c .scenario-description { border-color: #F59E0B; }

.scenario-d {
  background: linear-gradient(135deg, #F5F3FF 0%, #EDE9FE 100%);
  border: 1px solid #DDD6FE;
}
.scenario-d h3 { border-color: #8B5CF6; }
.scenario-d .scenario-description { border-color: #8B5CF6; }

/* Enhanced visualization for option boxes */
.option_box {
  background: linear-gradient(to right, #FFFFFF 0%, #FAFAFA 100%);
  transition: all 0.3s ease;
  position: relative;
}

.option_box:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.option_box.rank_1 {
  border-left: 6px solid #10B981;
  background: linear-gradient(to right, rgba(209, 250, 229, 0.3) 0%, rgba(255, 255, 255, 1) 100%);
}

.option_box.rank_2 {
  border-left: 6px solid #F59E0B;
  background: linear-gradient(to right, rgba(254, 243, 199, 0.3) 0%, rgba(255, 255, 255, 1) 100%);
}

.option_box .summary {
  background-color: rgba(255,255,255,0.7);
  border-radius: 4px;
  padding: 6px 10px;
  margin-bottom: 12px;
  display: inline-block;
  font-weight: 500;
}

.option_box.rank_1 .summary { 
  color: #047857; 
  border-bottom: 1px dashed #A7F3D0;
}

.option_box.rank_2 .summary { 
  color: #B45309; 
  border-bottom: 1px dashed #FDE68A;
}

/* Improved buttons */
button.primary {
  background: linear-gradient(to right, #0D9488 0%, #14B8A6 100%) !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
}

button.primary:hover {
  background: linear-gradient(to right, #0F766E 0%, #0D9488 100%) !important;
  box-shadow: 0 4px 8px rgba(0,0,0,0.2) !important;
}

/* Tab styling enhancement */
.tab-nav button {
  transition: all 0.3s ease;
  position: relative;
}

.tab-nav button.selected {
  color: #0F766E !important;
  background-color: rgba(209, 250, 229, 0.2);
}
"""


# --- Function to run the scenario analysis ---
def run_scenario_analysis(scenario_choice, custom_json_input=""):
    """Main orchestration function to run the agent workflow."""
    start_time = datetime.now()
    logging.info(f"Running Analysis: Scenario '{scenario_choice}'")
    
    discussion_log = [
        "# Agent Workflow Analysis Log",
        "",
        f"**Analysis Started:** {start_time.strftime('%Y-%m-%d %H:%M:%S')}",
        "",
        "## Agent System Initialization",
        "The multi-agent system has been initialized with a specialized agent for each analysis phase. Each agent has specific expertise and tools to handle different aspects of the tariff scenario analysis.",
        ""
    ]
    
    # Default error result
    error_result = {
        "top_options": [],
        "decision_rationale": "An error occurred during analysis.",
        "strategic_considerations": [
            "Analysis could not be completed due to an error."
        ],
        "full_ranking": [],
    }
    
    try:
        # Step 1: Load data
        discussion_log.append("## Data Loader Agent")
        discussion_log.append("**Capabilities:** Data validation, transformation, error handling")
        discussion_log.append("**Task:** Converting raw scenario data into structured format for analysis")
        discussion_log.append("")
        discussion_log.append("**Agent Processing:**")
        data_start_time = datetime.now()
        processed_options = agent_data_loader(scenario_choice, custom_json_input)
        data_time = (datetime.now() - data_start_time).total_seconds()
        
        if isinstance(processed_options, str) and processed_options.startswith("ERROR:"):
            error_msg = processed_options.replace("ERROR:", "").strip()
            discussion_log.append(f"❌ **Error:** Data loading failed: {error_msg}")
            discussion_log.append(f"**Processing Time:** {data_time:.2f}s")
            discussion_log.append("")
            return error_result, "\n".join(discussion_log)
            
        discussion_log.append(f"✅ **Success:** Validated and processed {len(processed_options)} options")
        discussion_log.append(f"**Processing Time:** {data_time:.2f}s")
        discussion_log.append("")
        discussion_log.append("**Data Output Example:**")
        if processed_options and len(processed_options) > 0:
            first_option = processed_options[0]
            # Format first option as JSON-like string for display
            formatted_option = "```json\n"
            formatted_option += "{\n"
            for key, value in first_option.items():
                formatted_option += f"  \"{key}\": {json.dumps(value)},\n"
            formatted_option = formatted_option.rstrip(",\n") + "\n}"
            formatted_option += "\n```"
            discussion_log.append(formatted_option)
        discussion_log.append("")
            
        # Step 2: Calculate costs
        discussion_log.append("## Cost Calculator Agent")
        discussion_log.append("**Role:** Calculates landed costs from base costs and duty rates")
        discussion_log.append("**Capabilities:** Financial calculations, cost structure analysis")
        discussion_log.append("**Task:** Computing landed costs incorporating tariffs/duties")
        discussion_log.append("")
        discussion_log.append("**Agent Processing:**")
        cost_start_time = datetime.now()
        options_with_cost = agent_cost_calculator(processed_options)
        cost_time = (datetime.now() - cost_start_time).total_seconds()
        
        if isinstance(options_with_cost, str) and options_with_cost.startswith("ERROR:"):
            error_msg = options_with_cost.replace("ERROR:", "").strip()
            discussion_log.append(f"❌ **Error:** Cost calculation failed: {error_msg}")
            discussion_log.append(f"**Processing Time:** {cost_time:.2f}s")
            discussion_log.append("")
            return error_result, "\n".join(discussion_log)
            
        discussion_log.append(f"✅ **Success:** Calculated landed costs for {len(options_with_cost)} options")
        discussion_log.append(f"**Processing Time:** {cost_time:.2f}s")
        discussion_log.append("")
        discussion_log.append("**Cost Calculation Example:**")
        if options_with_cost and len(options_with_cost) > 0:
            first_option = options_with_cost[0]
            supplier = first_option.get("supplier", "Unknown")
            base_cost = first_option.get("base_cost", 0)
            duty_rate = first_option.get("duty_rate", 0)
            landed_cost = first_option.get("landed_cost", 0)
            discussion_log.append(f"Supplier: {supplier}")
            discussion_log.append(f"Base Cost: ${base_cost:.2f}")
            discussion_log.append(f"Duty Rate: {duty_rate*100:.2f}%")
            discussion_log.append(f"Landed Cost: ${landed_cost:.2f}")
        discussion_log.append("")
            
        # Step 3: Assess risks
        discussion_log.append("## Risk Assessor Agent")
        discussion_log.append("**Role:** Evaluates supply chain risks for each option")
        discussion_log.append("**Capabilities:** Risk modeling, geopolitical analysis, supply chain vulnerability assessment")
        discussion_log.append("**Task:** Assigning risk scores (1-5 scale) based on supplier location and other factors")
        discussion_log.append("")
        discussion_log.append("**Agent Processing:**")
        risk_start_time = datetime.now()
        options_with_risk = agent_risk_assessor(options_with_cost)
        risk_time = (datetime.now() - risk_start_time).total_seconds()
        
        if isinstance(options_with_risk, str) and options_with_risk.startswith("ERROR:"):
            error_msg = options_with_risk.replace("ERROR:", "").strip()
            discussion_log.append(f"❌ **Error:** Risk assessment failed: {error_msg}")
            discussion_log.append(f"**Processing Time:** {risk_time:.2f}s")
            discussion_log.append("")
            return error_result, "\n".join(discussion_log)
            
        discussion_log.append(f"✅ **Success:** Assigned risk scores for {len(options_with_risk)} options")
        discussion_log.append(f"**Processing Time:** {risk_time:.2f}s")
        discussion_log.append("")
        discussion_log.append("**Risk Assessment Example:**")
        if options_with_risk and len(options_with_risk) > 0:
            first_option = options_with_risk[0]
            supplier = first_option.get("supplier", "Unknown")
            risk_hint = first_option.get("risk_hint", "Not provided")
            risk_score = first_option.get("risk_score", "Unknown")
            discussion_log.append(f"Supplier: {supplier}")
            discussion_log.append(f"Risk Hint: {risk_hint}")
            discussion_log.append(f"Risk Score: {risk_score}/5")
        discussion_log.append("")
            
        # Step 4: Determine strategy
        discussion_log.append("## Strategy Determiner Agent")
        discussion_log.append("**Role:** Analyzes all data points to recommend optimal sourcing strategy")
        discussion_log.append("**Capabilities:** Decision modeling, multi-factor analysis, strategic recommendation")
        discussion_log.append("**Task:** Ranking options and providing strategic recommendations with rationale")
        discussion_log.append("")
        discussion_log.append("**Agent Processing:**")
        strategy_start_time = datetime.now()
        final_analysis = agent_strategy_determiner(options_with_risk)
        strategy_time = (datetime.now() - strategy_start_time).total_seconds()
        
        if isinstance(final_analysis, str) and final_analysis.startswith("ERROR:"):
            error_msg = final_analysis.replace("ERROR:", "").strip()
            discussion_log.append(f"❌ **Error:** Strategy determination failed: {error_msg}")
            discussion_log.append(f"**Processing Time:** {strategy_time:.2f}s")
            discussion_log.append("")
            return error_result, "\n".join(discussion_log)
            
        discussion_log.append(f"✅ **Success:** Completed strategic analysis and ranking")
        discussion_log.append(f"**Processing Time:** {strategy_time:.2f}s")
        discussion_log.append("")
        
        # Include summary of top option from analysis
        top_options = final_analysis.get("top_options", [])
        if top_options and len(top_options) > 0:
            top_option = top_options[0]
            supplier = top_option.get("supplier", "Unknown")
            rank = top_option.get("rank", "?")
            summary = top_option.get("summary", "No summary available")
            discussion_log.append("**Top Recommendation:**")
            discussion_log.append(f"Option {rank}: {supplier} - {summary}")
        discussion_log.append("")
        
        # Success!
        global analysis_results_storage
        analysis_results_storage = final_analysis
        
        # Add overall summary with timing
        total_elapsed = (datetime.now() - start_time).total_seconds()
        discussion_log.append("## Analysis Summary")
        discussion_log.append(f"**Total Processing Time:** {total_elapsed:.2f}s")
        discussion_log.append(f"**Completed:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        discussion_log.append("")
        discussion_log.append("**Agent Collaboration:** The multi-agent system successfully completed all analysis stages through coordinated information flow. Each specialized agent built upon the previous agent's output to deliver a comprehensive tariff scenario recommendation.")
        
        return final_analysis, "\n".join(discussion_log)
        
    except Exception as e:
        logging.error(f"Unhandled exception during analysis: {e}", exc_info=True)
        discussion_log.append(f"## System Error")
        discussion_log.append(f"❌ **Unexpected error:** {str(e)}")
        discussion_log.append("")
        discussion_log.append("The agent system encountered an unexpected error. Please check the logs for detailed information or try a different scenario.")
        return error_result, "\n".join(discussion_log)


# --- News API Fetch Function ---
def fetch_trade_news(
    query="international trade tariffs OR global supply chain news OR trade policy",
    count=10,
):
    """Fetches relevant news using the Currents API."""
    logging.info(
        f"--- fetch_trade_news called with query: '{query}' ---"
    )  # Added entry log

    # 1. Check API Key
    api_key = os.getenv("CURRENTS_API_KEY")
    if not api_key:
        logging.error("CURRENTS_API_KEY not found in env.")
        return "<p style='color: red;'>⚠️ Error: Currents API Key missing.</p>"
    else:
        logging.info("Currents API Key found.")  # Log key found

    # 2. Prepare API Request
    endpoint = "https://api.currentsapi.services/v1/latest-news"
    params = {
        "keywords": query,  # Correct parameter for Currents API search
        "language": "en",
        "apiKey": api_key,
    }
    logging.info(f"Currents API Request Params: {params}")  # Log params

    # 3. Make API Call
    try:
        response = requests.get(endpoint, params=params, timeout=15)
        logging.info(
            f"Currents API Response Status Code: {response.status_code}"
        )  # Log status code
        response.raise_for_status()
        news_data = response.json()

        # 4. Parse and Format Results (Currents API structure)
        if news_data.get("status") != "ok":
            error_msg = news_data.get("message", "Unknown API error")
            logging.error(
                f"Currents API Error Status '{news_data.get('status')}': {error_msg}"
            )
            return f"<p style='color: red;'>⚠️ News API Error: {html.escape(error_msg)}</p>"
        articles = news_data.get("news", [])
        if not articles:
            return "<p><i>No relevant news found.</i></p>"

        articles_to_display = articles[:count]
        output_md = (
            "### Latest Global Trade & Business News (Currents API)\n\n"
        )
        for article in articles_to_display:
            title = html.escape(article.get("title", "No Title"))
            url = article.get("url", "#")
            description = html.escape(article.get("description", "N/A"))
            provider = html.escape(article.get("author", "Unknown Source"))
            date_published_str = article.get("published", "")
            date_display = ""
            if date_published_str:
                try:
                    date_obj = datetime.strptime(
                        date_published_str, "%Y-%m-%d %H:%M:%S +0000"
                    )
                    date_display = (
                        f" - <i>{date_obj.strftime('%Y-%m-%d %H:%M')} UTC</i>"
                    )
                except ValueError:
                    date_display = f" - <i>{date_published_str[:10]}</i>"
            output_md += f"**[{title}]({url})**\n> {description}\n> <small>Source: {provider}{date_display}</small>\n\n---\n"

        logging.info(
            f"Fetched and formatted {len(articles_to_display)} news articles."
        )
        return output_md

    except requests.exceptions.Timeout:
        logging.error("Currents API request timed out.")
        return (
            "<p style='color: orange;'>⚠️ Warning: News request timed out.</p>"
        )
    except requests.exceptions.HTTPError as e:
        logging.error(
            f"Currents API HTTP error {e.response.status_code}: {e.response.text[:200]}"
        )
        return f"<p style='color: red;'>⚠️ News HTTP Error {e.response.status_code}. Check Key/Plan.</p>"
    except requests.exceptions.RequestException as e:
        logging.error(f"Currents API connection error: {e}")
        return "<p style='color: red;'>⚠️ Cannot connect to news service.</p>"
    except Exception as e:
        logging.error(f"News fetch error: {e}", exc_info=True)
        return "<p style='color: red;'>⚠️ Unexpected news error.</p>"


# --- Email Dispatch Function ---
def dispatch_results_email(selected_option):
    """Formats results for the selected option and dispatches via Logic App."""
    if not email_feature_enabled:
        logging.warning("Email dispatch unavailable - missing environment variables.")
        gr.Warning("Email Feature Disabled: Both LOGICAPP_ENDPOINT and EMAIL_RECIPIENT environment variables must be set.")
        return "✖ Feature Disabled: Configure email settings in .env file."
        
    # Get environment variables
    logic_app_url = os.getenv("LOGICAPP_ENDPOINT")
    recipient_addr = os.getenv("EMAIL_RECIPIENT", os.getenv("MAIL_RECIPIENT", ""))
    
    # Double-check required vars are set (shouldn't reach here if email_feature_enabled is False)
    if not logic_app_url or not recipient_addr:
        logging.warning("Email dispatch failed - missing required environment variables.")
        gr.Warning("Email Configuration Error: Missing endpoint or recipient address.")
        return "✖ Configuration Error: Missing email settings."
        
    # 1. Verify we have analysis results
    global analysis_results_storage
    if not analysis_results_storage or not analysis_results_storage.get("top_options"):
        logging.warning("Dispatch failed: No valid analysis results in storage.")
        gr.Warning("No Analysis Data: Run analysis before dispatching.")
        return "✖ Error: No valid analysis results found."

    if not selected_option:
         logging.warning("Dispatch failed: No option selected in the UI.")
         return "✖ Error: No option selected."

    # 2. Find Selected Option Data
    selected_option_data = None
    try:
        logging.debug(f"Parsing selected identifier: '{selected_option}'")
        parts = selected_option.split(":")
        if len(parts) < 1:
            raise ValueError(f"Invalid option format: {selected_option}")
            
        selected_rank_str = parts[0].replace("Option", "").strip()
        selected_rank = int(selected_rank_str)
        logging.debug(f"Parsed rank: {selected_rank}")

        for option in analysis_results_storage.get("top_options", []):
            if option.get("rank") == selected_rank:
                selected_option_data = option
                logging.debug(f"Found matching option data for rank {selected_rank}: {option.get('supplier')}")
                break
                
        if selected_option_data is None:
            raise ValueError(f"Could not find data for selected rank {selected_rank}")

    except (IndexError, ValueError, TypeError) as e:
        logging.error(f"Error parsing selected option identifier '{selected_option}': {e}", exc_info=True)
        gr.Error(f"Data Error: Could not identify details for selected option '{selected_option}'.")
        return f"✖ Data Error: {str(e)}"

    # 3. Format Email Content
    try:
        # Get data
        supplier = selected_option_data.get("supplier", "Unknown")
        summary = selected_option_data.get("summary", "No summary available")
        details = selected_option_data.get("details", {})
        score = details.get("score", 0)
        landed_cost = details.get("landed_cost", 0)
        risk = details.get("risk", 0)
        lead_time = details.get("lead_time", 0)
        
        rationale = analysis_results_storage.get("decision_rationale", "No rationale provided.")
        considerations = analysis_results_storage.get("strategic_considerations", ["No considerations provided."])
        
        # Format HTML body
        subject = f"Tariff Scenario Analysis: Option {selected_option_data.get('rank', '?')} ({supplier})"
        
        body = f"""
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 800px; margin: 0 auto; padding: 20px; }}
                h1 {{ color: #0F766E; }}
                h2 {{ color: #0F766E; border-bottom: 1px solid #ddd; padding-bottom: 10px; }}
                .option-card {{ border-left: 4px solid #10B981; background-color: #f9f9f9; padding: 15px; margin-bottom: 20px; }}
                .score {{ font-size: 1.2em; font-weight: bold; color: #0F766E; }}
                .details {{ margin-top: 15px; }}
                .detail-item {{ margin-bottom: 8px; }}
                .detail-label {{ font-weight: bold; }}
                .considerations {{ background-color: #FFFBEB; border: 1px solid #FDE68A; padding: 15px; margin-top: 20px; }}
                .consideration-item {{ margin-bottom: 10px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1>Tariff Scenario Analysis Report</h1>
                
                <p><strong>Date:</strong> {datetime.now().strftime("%Y-%m-%d %H:%M")}</p>
                
                <div class="option-card">
                    <h2>Selected Option: {supplier}</h2>
                    <p><strong>Summary:</strong> {summary}</p>
                    
                    <div class="details">
                        <div class="detail-item">
                            <span class="detail-label">Overall Score:</span> 
                            <span class="score">{score:.4f}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Landed Cost:</span> ${landed_cost:,.2f}
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Risk Level:</span> {risk}/5
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Lead Time:</span> {lead_time} days
                        </div>
                    </div>
                </div>
                
                <h2>Decision Rationale</h2>
                <p>{rationale}</p>
                
                <div class="considerations">
                    <h2>Strategic Considerations</h2>
                    <ul>
        """
        
        for item in considerations:
            body += f"<li class='consideration-item'>{item}</li>\n"
            
        body += """
                    </ul>
                </div>
                
                <p style="margin-top: 30px; color: #666; font-size: 0.9em;">
                    This report was generated by the Tariff Scenario Planner Dashboard. 
                    Please do not reply to this email as it was sent from an automated system.
                </p>
            </div>
        </body>
        </html>
        """
        
        logging.debug(f"Email body formatted. Length: {len(body)}")

    except Exception as e:
        logging.error(f"Error formatting email content: {e}", exc_info=True)
        gr.Error("Formatting Error: Failed to construct the email content.")
        return f"✖ Error: Failed to format email content."

    # 4. Send Request to Logic App
    payload = {"Recipient": recipient_addr, "Subject": subject, "Body": body}
    endpoint_url = logic_app_url
    logging.info(f"Dispatching POST request to Logic App URL: {endpoint_url}")
    logging.debug(f"Payload - Recipient: {recipient_addr}, Subject: {subject[:50]}..., Body Len: {len(body)}")

    try:
        headers = {"Content-Type": "application/json"}
        response = requests.post(
            endpoint_url, json=payload, headers=headers, timeout=30
        )
        
        status_code = response.status_code
        logging.info(f"Logic App response status code: {status_code}")
        
        if 200 <= status_code < 300:
            success_msg = f"✔ Analysis report for Option {selected_option_data.get('rank', '?')} ({supplier}) dispatched successfully."
            logging.info(success_msg)
            return success_msg
        else:
            error_msg = f"Logic App returned error: {status_code}"
            logging.error(f"{error_msg}: {response.text}")
            gr.Error(f"Dispatch Error: Logic App returned status code {status_code}")
            return f"✖ Dispatch Failed: Logic App returned error {status_code}."
            
    except requests.exceptions.ConnectionError as e:
        logging.error(f"Connection Error: {e}", exc_info=True)
        gr.Error("Connection Error: Could not connect to the Logic App URL.")
        return "✖ Dispatch Failed: Cannot connect to the Logic App URL."
    except requests.exceptions.Timeout as e:
        logging.error(f"Timeout Error dispatching email: {e}", exc_info=True)
        gr.Error("Timeout Error: The request to the email service timed out.")
        return "✖ Dispatch Failed: Request timed out."
    except Exception as e:
        logging.error(f"Unexpected error during email dispatch: {e}", exc_info=True)
        gr.Error(f"Unexpected Error: An error occurred ({type(e).__name__}) while sending the email.")
        return f"✖ Dispatch Failed: An unexpected error occurred ({type(e).__name__})."


# --- Global variables for storing analysis results ---
analysis_results_storage = None

# --- SAP OData Connector ---
class SAPODataConnector:
    """Connector for SAP data using OData services"""
    
    def __init__(self, verify_ssl=False):
        """Initialize with credentials from environment variables"""
        self.username = os.getenv("SAP_USERNAME")
        self.password = os.getenv("SAP_PASSWORD")
        self.base_url = "https://sapes5.sapdevcenter.com/sap/opu/odata/IWBEP/GWSAMPLE_BASIC"
        self.headers = {
            "Accept": "application/json",
            "x-csrf-token": "Fetch"  
        }
        self.cookies = None
        self.verify_ssl = verify_ssl
        
        if not self.verify_ssl:
            logging.info("⚠️ SSL verification disabled for SAP connections. This is insecure for production use.")
        
        logging.info(f"Configured SAP OData Service URL: {self.base_url}")
        logging.info(f"🔒 SSL Verification: {'Enabled' if self.verify_ssl else 'Disabled'}")

    def test_connection(self):
        """Test connection to SAP OData service"""
        try:
            logging.info(f"🔄 Testing connection to: {self.base_url}")
            
            # First, do a metadata request to get cookies and CSRF token
            metadata_url = f"{self.base_url}/$metadata"
            metadata_response = requests.get(
                metadata_url,
                auth=(self.username, self.password),
                headers={"Accept": "application/xml"},
                params={"sap-client": "002"},
                verify=self.verify_ssl
            )
            logging.info(f"📡 Metadata status: {metadata_response.status_code}")
            self.cookies = metadata_response.cookies
            
            # Get CSRF token if needed for any POST operations
            if 'x-csrf-token' in metadata_response.headers:
                self.headers['x-csrf-token'] = metadata_response.headers['x-csrf-token']
            
            metadata_response.raise_for_status()
            logging.info("✅ SAP connection test successful")
            return True
        except Exception as e:
            logging.error(f"❌ SAP Connection Error: {str(e)}")
            return False

    def fetch_entity_data(self, entity_set, top=100):
        """Fetch data from any entity set"""
        try:
            url = f"{self.base_url}/{entity_set}"
            
            # Add required parameters
            if "?" in url:
                url += "&sap-client=002&$format=json"
            else:
                url += "?sap-client=002&$format=json"
                
            if top:
                url += f"&$top={top}"
                
            logging.info(f"🔄 Fetching {entity_set} from: {url}")
            
            # Make the request with authentication and cookies
            response = requests.get(
                url,
                auth=(self.username, self.password),
                headers=self.headers,
                cookies=self.cookies,   
                verify=self.verify_ssl
            )
            response.raise_for_status()
            
            # Parse the data
            data = response.json()
            results = data.get('d', {}).get('results', [])
            logging.info(f"✅ Successfully fetched {len(results)} items from {entity_set}")
            
            return results
        except Exception as e:
            logging.error(f"❌ Error fetching {entity_set}: {str(e)}")
            return []

    def fetch_products(self):
        """Fetch products from SAP"""
        return self.fetch_entity_data("ProductSet", 50)

    def fetch_orders(self):
        """Fetch sales orders from SAP"""
        return self.fetch_entity_data("SalesOrderSet", 50)

    def fetch_order_items(self):
        """Fetch sales order line items from SAP"""
        return self.fetch_entity_data("SalesOrderLineItemSet", 100)

    def fetch_business_partners(self):
        """Fetch business partners from SAP"""
        return self.fetch_entity_data("BusinessPartnerSet", 50)

    def get_sap_context_data(self):
        """Fetch and process SAP data for the context tab"""
        try:
            # Fetch required data
            orders = self.fetch_orders()
            products = self.fetch_products()
            partners = self.fetch_business_partners()
            line_items = self.fetch_order_items()
            
            if not orders or not products:
                logging.warning("Failed to retrieve SAP data for context")
                return None
                
            # Process financial metrics
            total_sales = sum(float(order.get('GrossAmount', 0)) for order in orders)
            avg_order_value = total_sales / len(orders) if orders else 0
            
            # Revenue by currency
            revenue_by_currency = {}
            for order in orders:
                currency = order.get('CurrencyCode', 'Unknown')
                amount = float(order.get('GrossAmount', 0))
                if currency in revenue_by_currency:
                    revenue_by_currency[currency] += amount
                else:
                    revenue_by_currency[currency] = amount
            
            # Top customers by sales
            sales_by_customer = {}
            for order in orders:
                customer_id = order.get('CustomerID')
                amount = float(order.get('GrossAmount', 0))
                if customer_id in sales_by_customer:
                    sales_by_customer[customer_id] += amount
                else:
                    sales_by_customer[customer_id] = amount
            
            # Map customer IDs to names
            customer_map = {bp.get('BusinessPartnerID'): bp.get('CompanyName') 
                          for bp in partners if bp.get('BusinessPartnerID')}
            
            # Top customers
            top_customers = sorted(
                [(customer_id, amount) for customer_id, amount in sales_by_customer.items()],
                key=lambda x: x[1], reverse=True
            )[:5]
            
            # Products analysis
            product_analysis = {
                "total_products": len(products),
                "avg_price": sum(float(p.get('Price', 0)) for p in products) / len(products) if products else 0,
            }
            
            context_data = {
                "transaction_date": datetime.now().strftime("%Y-%m-%d"),
                "system_source": "SAP S/4HANA (OData Service)",
                "connection_status": "Connected",
                "procurement_context": {
                    "annual_spend": f"${total_sales:,.2f}",
                    "active_suppliers": len({p.get('BusinessPartnerID') for p in partners if p.get('BusinessPartnerID')}),
                    "order_count": len(orders),
                },
                "trade_context": {
                    "currencies": list(revenue_by_currency.keys()),
                    "revenue_by_currency": {currency: f"${amount:,.2f}" for currency, amount in revenue_by_currency.items()},
                    "top_markets": [customer_map.get(cust_id, "Unknown") for cust_id, _ in top_customers[:3]]
                },
                "supply_chain_kpis": {
                    "products_count": product_analysis["total_products"],
                    "avg_product_price": f"${product_analysis['avg_price']:,.2f}",
                    "avg_order_value": f"${avg_order_value:,.2f}",
                    "line_items_count": len(line_items)
                },
                "sample_orders": orders[:5]
            }
            
            return context_data
            
        except Exception as e:
            logging.error(f"Error processing SAP context data: {str(e)}", exc_info=True)
            return None


# --- Launch the UI when run directly ---
if __name__ == "__main__":
    # Check required environment variables
    print("--- Tariff Scenario Planner v4 ---")
    print("Checking environment variables...")
    env_vars_ok, env_vars_msg = check_env_vars()
    
    if not env_vars_ok:
        print(f"\n⚠️ WARNING: Missing Required Environment Variables ⚠️\n{env_vars_msg}\n")
        print("Some features may be disabled. Check your .env file.")
    else:
        print("✅ All required environment variables are set.")
    
    print("Creating and launching UI...")
    demo = create_ui()
    demo.launch(server_name="127.0.0.1", server_port=7861)