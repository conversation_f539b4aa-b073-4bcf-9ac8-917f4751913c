import os
import json
import requests
import gradio as gr
import graphviz
from graphviz import Digraph
from datetime import datetime
from dotenv import load_dotenv
from autogen import AssistantAgent, UserProxyAgent
import urllib3

# Disable SSL warning messages - important when using verify=False
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Load environment variables from .env file
load_dotenv()

########################################
# Helper Function to Extract Message
########################################

def extract_message(chat_result):
    """
    Extracts the final message content from a ChatResult object.
    Removes markdown code block markers if present.
    """
    try:
        if hasattr(chat_result, "chat_history") and isinstance(chat_result.chat_history, list) and chat_result.chat_history:
            content = chat_result.chat_history[-1].get("content", "")
        else:
            content = str(chat_result)
    except Exception:
        content = str(chat_result)
    if content.startswith("```json"):
        content = content[7:]
    if content.endswith("```"):
        content = content[:-3]
    return content.strip()

########################################
# SAP Finance Data Connector (OData)
########################################

########################################
# SAP Finance Data Connector (OData)
########################################

class SAPFinanceConnector:
    """Connector for SAP Finance data using OData services"""
    
    def __init__(self, verify_ssl=False):  # Default to False to disable SSL verification
        """Initialize with credentials from environment variables"""
        self.username = os.getenv("SAP_USERNAME")
        self.password = os.getenv("SAP_PASSWORD")
        self.base_url = "https://sapes5.sapdevcenter.com/sap/opu/odata/IWBEP/GWSAMPLE_BASIC"
        self.headers = {
            "Accept": "application/json",
            "x-csrf-token": "Fetch"  
        }
        self.cookies = None  # Will store cookies after first request
        self.verify_ssl = verify_ssl  # Store the SSL verification setting
        
        # Show warning if SSL verification is disabled
        if not self.verify_ssl:
            print("⚠️ Warning: SSL verification disabled for SAP connections. This is insecure for production use.")
            # We already import and disable warnings at the module level
            # Additional safety to ensure warnings are suppressed
            import urllib3
            urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
        
        print(f"Configured SAP OData Service URL: {self.base_url}")
        print(f"🔒 SSL Verification: {'Enabled' if self.verify_ssl else 'Disabled'}")

    def test_connection(self):
        """Test connection to SAP OData service"""
        try:
            print(f"🔄 Testing connection to: {self.base_url}")
            
            # First, do a metadata request to get cookies and CSRF token
            metadata_url = f"{self.base_url}/$metadata"
            metadata_response = requests.get(
                metadata_url,
                auth=(self.username, self.password),
                headers={"Accept": "application/xml"},
                params={"sap-client": "002"},
                verify=self.verify_ssl  # Use the verify_ssl attribute
            )
            print(f"📡 Metadata status: {metadata_response.status_code}")
            self.cookies = metadata_response.cookies
            
            # Get CSRF token if needed for any POST operations
            if 'x-csrf-token' in metadata_response.headers:
                self.headers['x-csrf-token'] = metadata_response.headers['x-csrf-token']
            
            metadata_response.raise_for_status()
            print("✅ SAP Finance connection test successful")
            return True
        except Exception as e:
            print(f"❌ SAP Finance Connection Error: {str(e)}")
            return False

    def fetch_entity_data(self, entity_set, top=100):
        """Fetch data from any entity set"""
        try:
            url = f"{self.base_url}/{entity_set}"
            
            # Add required parameters
            if "?" in url:
                url += "&sap-client=002&$format=json"
            else:
                url += "?sap-client=002&$format=json"
                
            if top:
                url += f"&$top={top}"
                
            print(f"🔄 Fetching {entity_set} from: {url}")
            
            # Make the request with authentication and cookies
            response = requests.get(
                url,
                auth=(self.username, self.password),
                headers=self.headers,
                cookies=self.cookies,   
                verify=self.verify_ssl  # Use the verify_ssl attribute
            )
            response.raise_for_status()
            
            # Parse the data
            data = response.json()
            results = data.get('d', {}).get('results', [])
            print(f"✅ Successfully fetched {len(results)} items from {entity_set}")
            
            return results
        except Exception as e:
            print(f"❌ Error fetching {entity_set}: {str(e)}")
            return []

    def fetch_products(self):
        """Fetch products from SAP"""
        return self.fetch_entity_data("ProductSet", 50)

    def fetch_orders(self):
        """Fetch sales orders from SAP"""
        return self.fetch_entity_data("SalesOrderSet", 50)

    def fetch_order_items(self):
        """Fetch sales order line items from SAP"""
        return self.fetch_entity_data("SalesOrderLineItemSet", 100)

    def fetch_business_partners(self):
        """Fetch business partners from SAP"""
        return self.fetch_entity_data("BusinessPartnerSet", 50)

########################################
# Data & News Connectors
########################################

class NewsDataConnector:
    def __init__(self):
        self.api_key = os.getenv("CURRENTS_API_KEY")
        self.base_url = "https://api.currentsapi.services/v1/latest-news"
        if self.api_key:
            print("\n📰 News Data Connector initialized")
        else:
            print("\n⚠️ Currents API key not found in .env file - skipping news analysis")

    def fetch_financial_news(self):
        if not self.api_key:
            return []
        try:
            params = {
                "language": "en",
                "apiKey": self.api_key,
                "category": "business,finance"
            }
            print("Fetching financial news from Currents API...")
            response = requests.get(self.base_url, params=params)
            response.raise_for_status()
            news_data = response.json()
            articles = news_data.get('news', [])
            processed_news = []
            for article in articles[:5]:
                processed_news.append({
                    'title': article.get('title', 'No title'),
                    'description': article.get('description', 'No description'),
                    'category': article.get('category', []),
                    'published': article.get('published', 'No date')
                })
            print(f"✅ Successfully fetched {len(processed_news)} news articles")
            return processed_news
        except Exception as e:
            print(f"❌ Error fetching news: {str(e)}")
            return []

########################################
# Dummy Salesforce Data Functions
########################################

def fetch_salesforce_data():
    """
    Simulates fetching dummy Salesforce CRM data.
    Replace with real API calls when needed.
    """
    dummy_data = {
        "TotalPipelineValue": 1500000,
        "ClosedDealsValue": 750000,
        "OpenOpportunities": 25,
        "ClosedDealsCount": 15,
        "OpportunityConversionRate": 60,
        "AverageDealSize": 50000,
        "CustomerChurnRate": 5,
        "TopCustomers": [
            {"Name": "Acme Corp", "Revenue": 200000},
            {"Name": "Beta Inc", "Revenue": 150000},
            {"Name": "Gamma LLC", "Revenue": 100000}
        ]
    }
    return dummy_data

def process_salesforce_data(sf_data):
    """
    Processes dummy Salesforce data into a summary string.
    """
    summary = (
        "### Salesforce CRM Data Summary\n"
        f"Total Pipeline Value: ${sf_data['TotalPipelineValue']:,}\n"
        f"Closed Deals Value: ${sf_data['ClosedDealsValue']:,}\n"
        f"Open Opportunities: {sf_data['OpenOpportunities']}\n"
        f"Closed Deals Count: {sf_data['ClosedDealsCount']}\n"
        f"Opportunity Conversion Rate: {sf_data['OpportunityConversionRate']}%\n"
        f"Average Deal Size: ${sf_data['AverageDealSize']:,}\n"
        f"Customer Churn Rate: {sf_data['CustomerChurnRate']}%\n"
        "Top Customers:\n"
    )
    for customer in sf_data["TopCustomers"]:
        summary += f"  - {customer['Name']}: ${customer['Revenue']:,}\n"
    return summary

########################################
# Data Processing & Agent Setup
########################################

def process_sap_data(orders, products, line_items, partners):
    """Process SAP OData financial data for dashboard display"""
    try:
        # Calculate key financial metrics
        total_sales = sum(float(order.get('GrossAmount', 0)) for order in orders)
        avg_order_value = total_sales / len(orders) if orders else 0
        
        # Revenue by currency
        revenue_by_currency = {}
        for order in orders:
            currency = order.get('CurrencyCode', 'Unknown')
            amount = float(order.get('GrossAmount', 0))
            if currency in revenue_by_currency:
                revenue_by_currency[currency] += amount
            else:
                revenue_by_currency[currency] = amount
        
        # Top customers by sales
        sales_by_customer = {}
        for order in orders:
            customer_id = order.get('CustomerID')
            amount = float(order.get('GrossAmount', 0))
            if customer_id in sales_by_customer:
                sales_by_customer[customer_id] += amount
            else:
                sales_by_customer[customer_id] = amount
        
        # Map customer IDs to names
        customer_map = {bp.get('BusinessPartnerID'): bp.get('CompanyName') 
                      for bp in partners if bp.get('BusinessPartnerID')}
        
        # Top customers
        top_customers = sorted(
            [(customer_id, amount) for customer_id, amount in sales_by_customer.items()],
            key=lambda x: x[1], reverse=True
        )[:5]
        
        # Products analysis
        product_analysis = {
            "total_products": len(products),
            "avg_price": sum(float(p.get('Price', 0)) for p in products) / len(products) if products else 0,
        }
        
        return {
            "financial_summary": {
                "total_sales": total_sales,
                "avg_order_value": avg_order_value,
                "order_count": len(orders),
                "line_items_count": len(line_items),
                "revenue_by_currency": revenue_by_currency
            },
            "top_customers": [
                {"id": cust_id, "name": customer_map.get(cust_id, "Unknown"), "sales": amount}
                for cust_id, amount in top_customers
            ],
            "products_summary": product_analysis,
            "sample_orders": orders[:5],
            "sample_products": products[:5],
            "sample_line_items": line_items[:5]
        }
    except Exception as e:
        print(f"Error processing SAP data: {str(e)}")
        return None

def setup_agents():
    config_list = [{
        "model": os.getenv("MODEL_DEPLOYMENT_NAME", "gpt-4"),
        "api_key": os.getenv("API_KEY"),
        "azure_endpoint": os.getenv("AZURE_ENDPOINT"),
        "api_type": "azure",
        "api_version": os.getenv("MODEL_API_VERSION", "2024-02-15-preview")
    }]
    llm_config = {"config_list": config_list, "temperature": 0}

    coordinator = UserProxyAgent(
        name="coordinator",
        system_message="""Direct the analysis flow. Key rules:
        1. Never repeat previous analyses
        2. Only pass essential insights forward
        3. Keep communication focused and non-redundant""",
        human_input_mode="NEVER",
        max_consecutive_auto_reply=1,
        llm_config=llm_config
    )

    market_agent = AssistantAgent(
        name="Market_Agent",
        system_message="""Analyze market data for:
        1. Key market opportunities
        2. Customer behavior trends
        3. Geographic insights
        Keep responses concise and non-repetitive.""",
        llm_config=llm_config
    )

    finance_agent = AssistantAgent(
        name="Finance_Agent",
        system_message="""Analyze financial metrics for:
        1. Performance optimization
        2. Cost efficiency
        3. Growth opportunities
        4. Risk assessment
        Provide unique insights only.""",
        llm_config=llm_config
    )

    news_agent = AssistantAgent(
        name="News_Agent",
        system_message="""Extract from news:
        1. Market impacts and opportunities
        2. Industry changes and trends
        3. Strategic implications for business
        Focus on unique, non-redundant insights.""",
        llm_config=llm_config
    )

    strategy_agent = AssistantAgent(
        name="Strategy_Agent",
        system_message="""Create a strategic plan with three distinct options.
Each option must include:
- Implementation plan (actionable steps)
- ROI estimation
- Risk assessment
Provide the output in strict JSON format with keys "Option 1", "Option 2", and "Option 3". Do not include any additional commentary.""",
        llm_config=llm_config
    )
    return coordinator, market_agent, finance_agent, news_agent, strategy_agent

########################################
# Dashboard Analysis Function
########################################

def run_dashboard_analysis():
    """Main function to run the dashboard analysis with real SAP data"""
    # Use SAP connector instead of the Northwind connector
    if True:
        connector = SAPFinanceConnector(verify_ssl=False)  # Explicitly disable SSL verification
    if not connector.test_connection():
        return ("SAP Connection failed", "No data available", "", "", "", "", "")
    
    # Fetch data from SAP
    orders = connector.fetch_orders()
    products = connector.fetch_products()
    line_items = connector.fetch_order_items()
    partners = connector.fetch_business_partners()
    
    # Process the data
    processed_data = process_sap_data(orders, products, line_items, partners)
    if not processed_data:
        return ("SAP data processing error", "No data available", "", "", "", "", "")
    
    # Prepare SAP connection status with financial KPIs
    financial_summary = processed_data["financial_summary"]
    db_summary = (
        f"📡 SAP Finance Connection: ✅\n"
        f"💰 Total Sales: ${financial_summary['total_sales']:,.2f}\n"
        f"📊 Orders: {financial_summary['order_count']}\n"
        f"🛒 Products: {processed_data['products_summary']['total_products']}"
    )
    
    # Fetch Salesforce dummy data and prepare summary
    sf_data = fetch_salesforce_data()
    sf_summary = process_salesforce_data(sf_data)
    
    news_connector = NewsDataConnector()
    news_data = news_connector.fetch_financial_news()
    news_summary = "\n".join([f"{a['title']} ({a.get('published', 'N/A')})" for a in news_data]) if news_data else "No news available"
    
    # Continue with agent setup and analysis
    coordinator, market_agent, finance_agent, news_agent, strategy_agent = setup_agents()
    
    # Format the data for better AI analysis
    data_for_agents = {
        "financial_data": financial_summary,
        "top_customers": processed_data["top_customers"],
        "products": processed_data["sample_products"],
        "orders": processed_data["sample_orders"]
    }
    
    # Get insights from the agents
    market_raw = coordinator.initiate_chat(market_agent, message="Extract key market insights: " + json.dumps(data_for_agents))
    market_insights = extract_message(market_raw)
    
    finance_raw = coordinator.initiate_chat(finance_agent, message="Extract key financial insights: " + json.dumps(data_for_agents))
    finance_insights = extract_message(finance_raw)
    
    news_insights = ""
    if news_data:
        news_raw = coordinator.initiate_chat(news_agent, message="Extract key market implications: " + json.dumps(news_data))
        news_insights = extract_message(news_raw)
    
    # Format the analysis text
    analysis_text = (
        "### Quarterly Analysis Summary using real SAP Finance Data\n\n"
        f"**Market Insights:**\n{market_insights}\n\n"
        f"**Financial Insights:**\n{finance_insights}\n\n"
        f"**News Insights:**\n{news_insights}"
    )
    
    # Get strategic recommendations
    strategy_prompt = (
        f"Develop strategic recommendations based on the following insights from real SAP financial data:\n\n"
        f"MARKET INSIGHTS:\n{market_insights}\n\n"
        f"FINANCIAL INSIGHTS:\n{finance_insights}\n\n"
    )
    if news_insights:
        strategy_prompt += f"NEWS INSIGHTS:\n{news_insights}\n\n"
    strategy_prompt += (
        "Provide exactly three distinct strategic options in JSON format as follows:\n"
        '{\n  "Option 1": "Description for Option 1",\n  '
        '"Option 2": "Description for Option 2",\n  '
        '"Option 3": "Description for Option 3"\n}'
    )
    strategy_raw = coordinator.initiate_chat(strategy_agent, message=strategy_prompt)
    strategy_text = extract_message(strategy_raw)
    try:
        recs = json.loads(strategy_text)
        option1 = json.dumps(recs.get("Option 1", {}), indent=2)
        option2 = json.dumps(recs.get("Option 2", {}), indent=2)
        option3 = json.dumps(recs.get("Option 3", {}), indent=2)
    except Exception as e:
        option1, option2, option3 = strategy_text, "", ""
    return db_summary, sf_summary, analysis_text, news_summary, option1, option2, option3

########################################
# Interactive Option Selection & Email Dispatch
########################################

global_options = {}

def run_analysis_callback():
    db_summary, sf_summary, analysis_text, news_summary, opt1, opt2, opt3 = run_dashboard_analysis()
    global global_options
    global_options = {"Option 1": opt1, "Option 2": opt2, "Option 3": opt3}
    return db_summary, sf_summary, analysis_text, news_summary, opt1, opt2, opt3

def update_option(selected):
    global global_options
    if selected == "Option 1":
        return global_options.get("Option 1", "No details available.")
    elif selected == "Option 2":
        return global_options.get("Option 2", "No details available.")
    elif selected == "Option 3":
        return global_options.get("Option 3", "No details available.")
    else:
        return "No option selected."

def trigger_logicapp(selected_option, global_options):
    """
    Constructs an HTML-formatted email payload containing all strategic options and the final selected option,
    then sends an HTTP POST request to the LogicApps endpoint.
    """
    email_body = f"""
    <html>
      <body>
        <h2>Final Strategic Options to pick</h2>
        <div style="margin-bottom:20px;">
          <p><strong>Option 1:</strong><br>{global_options.get("Option 1", "No details available.")}</p>
          <p><strong>Option 2:</strong><br>{global_options.get("Option 2", "No details available.")}</p>
          <p><strong>Option 3:</strong><br>{global_options.get("Option 3", "No details available.")}</p>
        </div>
        <hr>
        <p>
          <span style="background-color:#d4edda; color:#155724; padding:8px; border-radius:5px; font-size:16px;">
            Final Selected Option by CFO office: <strong>{selected_option}</strong>
          </span>
        </p>
        <p><i>AI Agentic generated Email.</i></p>
        <p>Thank you,<br>AI-Agents Finance Automated Team</p>
      </body>
    </html>
    """
    
    # Build payload with capitalized keys as expected by LogicApps
    payload = {
        "Subject": "Final Strategic Options from SAP Dashboard",
        "Body": email_body,
        "Recipient": os.getenv("EMAIL_RECIPIENT", "<EMAIL>")
    }
    
    # Retrieve the LogicApps endpoint from environment variables
    logicapp_endpoint = os.getenv("LOGICAPP_ENDPOINT")
    if not logicapp_endpoint:
        return "LOGICAPP_ENDPOINT not set in environment variables."
    
    try:
        response = requests.post(logicapp_endpoint, json=payload)
        response.raise_for_status()  # Raise error for bad status codes
        return f"You have confirmed: {selected_option}. Email dispatched successfully."
    except Exception as e:
        return f"You have confirmed: {selected_option}. Failed to dispatch email: {str(e)}"

def confirm_selection(selected):
    global global_options
    if not global_options:
        return "No strategic options available. Please run analysis first."
    return trigger_logicapp(selected, global_options)

########################################
# Graphviz Agent Flow Diagram
########################################

def generate_agent_flow():
    """Generates a Graphviz diagram of the agent interaction flow."""
    # Set the Graphviz path explicitly
    os.environ["PATH"] += os.pathsep + "C:/Users/<USER>/Downloads/SAPSoftwaredownload/windows_10_cmake_Release_Graphviz-12.2.1-win64/Graphviz-12.2.1-win64/bin"
    
    # Create the diagram
    dot = Digraph(comment='Agent Flow')
    dot.node('Coordinator', 'Coordinator Agent')
    dot.node('Market', 'Market Agent')
    dot.node('Finance', 'Finance Agent')
    dot.node('News', 'News Agent')
    dot.node('Strategy', 'Strategy Agent')
    dot.edge('Coordinator', 'Market')
    dot.edge('Coordinator', 'Finance')
    dot.edge('Coordinator', 'News')
    dot.edge('Coordinator', 'Strategy')
    
    # Return the diagram as an SVG string
    return dot.pipe(format='svg').decode('utf-8')

########################################
# Gradio Dashboard UI with Custom CSS
########################################

css = """
.scrollable-box textarea {
    overflow-y: auto;
    max-height: 350px;
    font-family: Verdana, sans-serif;
    font-size: 14px;
}
.gradio-radio label {
    border: 2px solid transparent;
    padding: 10px;
    border-radius: 5px;
    cursor: pointer;
    margin-right: 10px;
}
.gradio-radio input[type="radio"]:checked + label {
    border-color: #28a745 !important;
    background-color: #d4edda;
}
"""

with gr.Blocks(css=css) as demo:
    gr.Markdown("# 🚀 SAP Enterprise Insight Deep Research Multi-Agentic Dashboard: Strategy Planner for Finance")
    gr.Markdown("For CFOs & Finance Leaders to review AI-driven analysis and select strategic recommendations generated by AI Agents using Deep Research Models")
    
    # Row for SAP DB connection status and Salesforce CRM Summary
    with gr.Row():
        db_status_box = gr.Textbox(label="SAP DB Connection Status", lines=4, elem_classes="scrollable-box")
        sf_status_box = gr.Textbox(label="Salesforce CRM Summary", lines=4, elem_classes="scrollable-box")
    
    # Row for AI Analysis Summary and Breaking News
    with gr.Row():
        analysis_box = gr.Textbox(label="AI Agentic Analysis Summary", lines=10, elem_classes="scrollable-box")
        news_box = gr.Textbox(label="Breaking News & Financial Trends", lines=10, elem_classes="scrollable-box")
    
    run_button = gr.Button("Execute Agentic flow to generate Analysis & Options >>")
    
    # Strategic options arranged side-by-side
    with gr.Row():
        option1_box = gr.Textbox(label="Option 1 (Read below!)", lines=8, elem_classes="scrollable-box")
        option2_box = gr.Textbox(label="Option 2 (Read below!)", lines=8, elem_classes="scrollable-box")
        option3_box = gr.Textbox(label="Option 3 (Read below!)", lines=8, elem_classes="scrollable-box")
    
    run_button.click(fn=run_analysis_callback, 
                     outputs=[db_status_box, sf_status_box, analysis_box, news_box, option1_box, option2_box, option3_box])
    
    gr.Markdown("## Select the best strategic Option for review:")
    with gr.Row():
        option_radio = gr.Radio(choices=["Option 1", "Option 2", "Option 3"], label="Select Option", value="Option 1")
    selected_option_box = gr.Textbox(label="Selected Option Details", lines=10, elem_classes="scrollable-box")
    option_radio.change(fn=update_option, inputs=option_radio, outputs=selected_option_box)
    
    confirm_button = gr.Button("Confirm Selection and Dispatch to office of CFO mailbox for review >>")
    confirmation_text = gr.Textbox(label="Confirmation for dispatch!", lines=2)
    confirm_button.click(fn=confirm_selection, inputs=option_radio, outputs=confirmation_text)
    
    # Optional Graphviz Agent Flow Diagram
    with gr.Row():
        flow_button = gr.Button("Show Agent Flow Diagram")
        flow_diagram = gr.HTML(label="Agent Flow Diagram")
    flow_button.click(fn=generate_agent_flow, outputs=flow_diagram)
    


########################################
# Main Execution
########################################

if __name__ == "__main__":
    print("🚀 Launching SAP Enterprise Insight Dashboard...")
    print("👉 Open the Gradio interface in your browser to interact with the dashboard.")
    demo.launch()