"""
Tariff Scenario Planner - Advanced Multi-Agent Analysis Tool
Provides sophisticated tariff analysis with UI dashboard and agent-based processing.
"""
import os
import json
import logging
import pandas as pd
from datetime import datetime
import gradio as gr
import plotly.express as px
import plotly.graph_objects as go

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Global storage
analysis_results_storage = {}

# --- Predefined Scenarios Data ---
PREDEFINED_SCENARIOS = {
    "A": {
        "name": "Pharma: China vs. Canada Packaging",
        "product": "Plastic syringe bodies", 
        "hs_code": "3926.90",
        "description": (
            "**Challenge:** Evaluate shifting syringe body sourcing from "
            "Shenzhen (high tariffs/risk) to Ontario (zero tariff)."
        ),
        "options": [
            {
                "supplier": "Shenzhen", 
                "base_cost": 0.50, 
                "duty": 25.0, 
                "lead_time": 35, 
                "risk_hint": "High"
            },
            {
                "supplier": "Ontario", 
                "base_cost": 0.65, 
                "duty": 0.0, 
                "lead_time": 20, 
                "risk_hint": "Moderate"
            }
        ]
    },
    "B": {
        "name": "Automotive: Mexico vs. Domestic Steel",
        "product": "Flat-rolled steel", 
        "hs_code": "7208.39",
        "description": (
            "**Challenge:** Compare sourcing steel from Ohio (domestic) vs. "
            "Monterrey, Mexico (lower base cost)."
        ),
        "options": [
            {
                "supplier": "Ohio", 
                "base_cost": 800.0, 
                "duty": 0.0, 
                "lead_time": 10, 
                "risk_hint": "Low"
            },
            {
                "supplier": "Monterrey", 
                "base_cost": 700.0, 
                "duty": 10.0, 
                "lead_time": 15, 
                "risk_hint": "Moderate"
            }
        ]
    },
    "C": {
        "name": "Electronics: Taiwan vs. Korea Semiconductors",
        "product": "Semiconductors",
        "hs_code": "8542.31",
        "description": (
            "**Challenge:** Evaluate sourcing semiconductors from two "
            "different regions with varying risk profiles."
        ),
        "options": [
            {
                "supplier": "Taiwan", 
                "base_cost": 2.0, 
                "duty": 15.0, 
                "lead_time": 25, 
                "risk_hint": "High"
            },
            {
                "supplier": "South Korea", 
                "base_cost": 2.2, 
                "duty": 5.0, 
                "lead_time": 20, 
                "risk_hint": "Low"
            }
        ]
    }
}


# --- Core Functions ---
def check_env_vars(*vars):
    """Checks if specified environment variables are set."""
    missing_vars = [v for v in vars if not os.getenv(v)]
    if missing_vars:
        message = (
            f"Missing required env vars: {', '.join(missing_vars)}. "
            f"Check .env file."
        )
        logging.error(message)
        return False, message
    return True, "All required environment variables are set."


def agent_data_loader(scenario_id, custom_json_str=None):
    """Loads and validates data for a specified scenario or custom JSON."""
    logging.info(f"DataAgent: Loading data for scenario '{scenario_id}'...")
    options_data = None
    
    if scenario_id in PREDEFINED_SCENARIOS:
        options_data = PREDEFINED_SCENARIOS[scenario_id].get("options", [])
    elif scenario_id == "Custom" and custom_json_str:
        try:
            custom_data = json.loads(custom_json_str)
            if isinstance(custom_data, dict) and "options" in custom_data:
                options_data = custom_data["options"]
        except json.JSONDecodeError as e:
            logging.error(f"Invalid custom JSON provided: {e}")
            return None
    else:
        logging.error(f"Invalid scenario ID provided: {scenario_id}")
        return None

    if not options_data:
        logging.error("No valid options data found.")
        return None

    processed_options = []
    required_keys = ["supplier", "base_cost", "duty", "lead_time"]
    
    for option in options_data:
        if not isinstance(option, dict):
            continue
        
        if not all(key in option for key in required_keys):
            continue
            
        try:
            supplier = str(option["supplier"])
            base_cost = float(option["base_cost"])
            duty_percent = float(option["duty"])
            lead_time = int(option["lead_time"])
            risk_hint = option.get("risk_hint")

            duty_rate = duty_percent / 100.0
            processed_options.append({
                "supplier": supplier,
                "base_cost": base_cost,
                "duty_rate": duty_rate,
                "lead_time": lead_time,
                "risk_hint": risk_hint
            })
        except Exception:
            continue

    if not processed_options:
        logging.error("No valid options were processed from the input data.")
        return None

    return processed_options


def agent_cost_calculator(processed_options):
    """Calculates landed costs for each option."""
    logging.info("CostAgent: Calculating landed costs...")
    
    if not isinstance(processed_options, list) or not processed_options:
        logging.error(
            "CostAgent received invalid input: Expected a non-empty list."
        )
        return None

    output_options = []
    for option in processed_options:
        try:
            base_cost = option['base_cost']
            duty_rate = option['duty_rate']
            landed_cost = base_cost * (1 + duty_rate)
            
            option_with_cost = option.copy()
            option_with_cost['landed_cost'] = landed_cost
            output_options.append(option_with_cost)
        except Exception as e:
            logging.warning(
                f"CostAgent: Error for {option.get('supplier', 'N/A')}: {e}"
            )
            continue

    if not output_options:
        logging.error(
            "CostAgent: Failed to calculate cost for any provided option."
        )
        return None

    return output_options


def agent_risk_assessor(options_with_cost):
    """Assesses risk scores for each option."""
    logging.info("RiskAgent: Assessing risks...")
    
    if not isinstance(options_with_cost, list) or not options_with_cost:
        logging.error(
            "RiskAgent received invalid input: Expected non-empty list."
        )
        return None

    risk_map = {"low": 1, "moderate": 3, "high": 5}
    output_options = []
    
    for option in options_with_cost:
        risk_hint = option.get('risk_hint')
        risk_score = 3  # Default risk score

        if isinstance(risk_hint, (int, float)) and 1 <= risk_hint <= 5:
            risk_score = int(round(risk_hint))
        elif isinstance(risk_hint, str):
            hint_lower = risk_hint.lower().strip()
            if hint_lower in risk_map:
                risk_score = risk_map[hint_lower]

        option_with_risk = option.copy()
        option_with_risk['risk_score'] = risk_score
        output_options.append(option_with_risk)

    return output_options


def calculate_scores(df):
    """Helper function to calculate scores for strategy determination."""
    # Normalization (higher score is better)
    min_cost = df['landed_cost'].min()
    max_cost = df['landed_cost'].max()
    cost_range = max_cost - min_cost
    
    if cost_range == 0:
        df['cost_norm'] = 1.0
    else:
        df['cost_norm'] = (max_cost - df['landed_cost']) / cost_range

    min_risk = df['risk_score'].min()
    max_risk = df['risk_score'].max()
    risk_range = max_risk - min_risk
    
    if risk_range == 0:
        df['risk_norm'] = 1.0
    else:
        risk_norm_inv = (df['risk_score'] - min_risk) / risk_range
        df['risk_norm'] = 1.0 - risk_norm_inv

    min_lt = df['lead_time'].min()
    max_lt = df['lead_time'].max()
    lt_range = max_lt - min_lt
    
    if lt_range == 0:
        df['lead_time_norm'] = 1.0
    else:
        df['lead_time_norm'] = (max_lt - df['lead_time']) / lt_range

    # Weighted Score Calculation
    cost_w, risk_w, lt_w = 0.5, 0.3, 0.2
    df['score'] = (
        cost_w * df['cost_norm'] +
        risk_w * df['risk_norm'] +
        lt_w * df['lead_time_norm']
    )
    
    return (
        df, min_cost, max_cost, cost_range, 
        min_lt, max_lt, lt_range
    )


def agent_strategy_determiner(options_with_risk):
    """Determines strategy, ranks options, and provides recommendations."""
    logging.info("StrategyAgent: Determining strategy...")
    
    if not isinstance(options_with_risk, list) or len(options_with_risk) < 1:
        logging.error(
            "StrategyAgent requires a list with at least one option dictionary."
        )
        return None

    try:
        # Create DataFrame and validate columns
        df = pd.DataFrame(options_with_risk)
        required_cols = ['supplier', 'landed_cost', 'risk_score', 'lead_time']
        if not all(col in df.columns for col in required_cols):
            logging.error(
                "StrategyAgent: Input DataFrame missing required columns."
            )
            return None
            
        # Calculate scores and get normalization parameters
        score_results = calculate_scores(df)
        df = score_results[0]
        min_cost = score_results[1]
        max_cost = score_results[2]
        cost_range = score_results[3]
        min_lt = score_results[4]
        max_lt = score_results[5]
        lt_range = score_results[6]
        
        # Ranking
        df = df.sort_values(
            by=['score', 'landed_cost'], 
            ascending=[False, True]
        )
        df['rank'] = range(1, len(df) + 1)

        # Prepare Output
        output_cols = [
            'rank', 'supplier', 'landed_cost', 
            'risk_score', 'lead_time', 'score'
        ]
        df_output = df[output_cols].rename(columns={'risk_score': 'risk'})
        df_output['landed_cost'] = df_output['landed_cost'].round(2)
        df_output['score'] = df_output['score'].round(4)
        full_ranking_list = df_output.to_dict(orient='records')

        # Generate top options summary
        top_options_summary = []
        risk_map_rev = {
            1: "Low", 
            2: "Low-Med", 
            3: "Medium", 
            4: "Med-High", 
            5: "High"
        }
        
        for i, row in df.head(3).iterrows():
            option_summary = {"rank": row['rank'], "supplier": row['supplier']}
            qualitative_summary = []

            # Cost Summary
            if cost_range == 0:
                qualitative_summary.append("Avg Cost")
            elif row['landed_cost'] <= min_cost + 0.1 * cost_range:
                qualitative_summary.append("Lowest Cost")
            elif row['landed_cost'] >= max_cost - 0.1 * cost_range:
                qualitative_summary.append("Highest Cost")
            else:
                qualitative_summary.append("Mid-Range Cost")

            # Risk Summary
            risk_value = int(row['risk_score'])
            risk_desc = risk_map_rev.get(risk_value, "Unknown")
            qualitative_summary.append(f"{risk_desc} Risk")

            # Lead Time Summary
            if lt_range == 0:
                qualitative_summary.append("Avg LT")
            elif row['lead_time'] <= min_lt + 0.1 * lt_range:
                qualitative_summary.append("Fastest LT")
            elif row['lead_time'] >= max_lt - 0.1 * lt_range:
                qualitative_summary.append("Slowest LT")
            else:
                qualitative_summary.append("Mid-Range LT")

            option_summary["summary"] = ", ".join(qualitative_summary)
            option_details = next(
                (item for item in full_ranking_list 
                if item['rank'] == row['rank']), 
                {}
            )
            option_summary["details"] = option_details
            top_options_summary.append(option_summary)

        # Generate strategic considerations
        strategic_considerations = generate_strategic_considerations(
            top_options_summary, 
            full_ranking_list
        )

        # Final result structure
        top_supplier = top_options_summary[0]['supplier']
        final_result = {
            "top_options": top_options_summary,
            "decision_rationale": (
                f"Recommend {top_supplier} as the best option based on scoring."
            ),
            "strategic_considerations": strategic_considerations,
            "full_ranking": full_ranking_list
        }
        
        return final_result
        
    except Exception as e:
        logging.error(f"StrategyAgent: Error in strategy determination: {e}")
        return None


def generate_strategic_considerations(top_options, full_ranking):
    """Generates strategic considerations based on analysis results."""
    considerations = []
    
    if not top_options:
        return ["No options available to analyze."]
    
    # Get top option
    top_option = top_options[0]
    top_details = top_option.get("details", {})
    
    # Check for high risk
    if top_details.get("risk", 0) >= 4:
        considerations.append(
            "⚠️ High Risk: The top option has significant risk exposure. "
            "Consider mitigation strategies or diversification."
        )
    
    # Compare with cheapest option if different
    min_cost_option = min(
        full_ranking, 
        key=lambda x: x.get('landed_cost', float('inf'))
    )
    if min_cost_option.get('rank') != top_details.get('rank'):
        cost_diff = (
            top_details.get('landed_cost', 0) - 
            min_cost_option.get('landed_cost', 0)
        )
        if cost_diff > 0:
            considerations.append(
                f"💰 Cost Premium: Top option costs ${cost_diff:.2f} more than "
                f"the cheapest option ({min_cost_option.get('supplier')}). "
                f"Evaluate if the risk/lead-time benefits justify this premium."
            )
    
    # Add general strategic advice
    considerations.append(
        "📊 Monitoring: Market conditions and tariff policies change "
        "frequently. Reevaluate this analysis quarterly."
    )
    
    return considerations


def run_scenario_analysis(scenario_choice, custom_json_input=""):
    """Main orchestration function to run the agent workflow."""
    start_time = datetime.now()
    logging.info(f"Running Analysis: Scenario '{scenario_choice}'")
    
    # Default error result
    error_result = {
        "top_options": [],
        "decision_rationale": "An error occurred during analysis.",
        "strategic_considerations": ["Analysis could not be completed."],
        "full_ranking": []
    }
    
    try:
        # Step 1: Load data
        processed_options = agent_data_loader(scenario_choice, custom_json_input)
        if processed_options is None:
            return error_result, "Data loading failed."
            
        # Step 2: Calculate costs
        options_with_cost = agent_cost_calculator(processed_options)
        if options_with_cost is None:
            return error_result, "Cost calculation failed."
            
        # Step 3: Assess risks
        options_with_risk = agent_risk_assessor(options_with_cost)
        if options_with_risk is None:
            return error_result, "Risk assessment failed."
            
        # Step 4: Determine strategy
        final_analysis = agent_strategy_determiner(options_with_risk)
        if final_analysis is None:
            return error_result, "Strategy determination failed."
            
        # Success!
        global analysis_results_storage
        analysis_results_storage = final_analysis
        
        total_elapsed = (datetime.now() - start_time).total_seconds()
        return final_analysis, f"Analysis complete. Time: {total_elapsed:.2f}s"
        
    except Exception as e:
        logging.error(f"Unhandled exception during analysis: {e}", exc_info=True)
        return error_result, f"Unexpected error: {str(e)}"


# --- UI Dashboard Functions ---
def create_comparison_chart(ranking_data):
    """Creates an interactive bar chart comparing options."""
    if not ranking_data:
        return None
        
    # Create a DataFrame for plotting
    df = pd.DataFrame(ranking_data)
    df = df.sort_values(by='rank')
    
    # Create chart
    fig = px.bar(
        df, 
        x='supplier', 
        y='score', 
        title='Option Comparison',
        color='score',
        color_continuous_scale='Viridis',
        labels={'score': 'Overall Score', 'supplier': 'Supplier'},
        hover_data=['landed_cost', 'risk', 'lead_time']
    )
    
    # Customize layout
    fig.update_layout(
        xaxis_title="Supplier",
        yaxis_title="Score (Higher is Better)",
        height=400,
        margin=dict(l=50, r=20, t=50, b=50)
    )
    
    return fig


def create_radar_chart(option_data):
    """Creates a radar chart for detailed option comparison."""
    if not option_data or len(option_data) < 2:
        return None
        
    categories = ['Cost Score', 'Risk Score', 'Lead Time Score']
    
    fig = go.Figure()
    
    # Process each option for the radar chart
    for option in option_data:
        details = option.get('details', {})
        
        # Custom calculations to transform raw scores into radar values (0-1)
        cost_score = 1 - ((details.get('landed_cost', 0) - 0) / 1000)
        risk_score = 1 - ((details.get('risk', 3) - 1) / 4)
        lt_score = 1 - ((details.get('lead_time', 0) - 0) / 40)
        
        # Clamp to reasonable range
        cost_score = max(0.1, min(1.0, cost_score))
        risk_score = max(0.1, min(1.0, risk_score))
        lt_score = max(0.1, min(1.0, lt_score))
        
        fig.add_trace(go.Scatterpolar(
            r=[cost_score, risk_score, lt_score],
            theta=categories,
            fill='toself',
            name=f"{option.get('supplier', 'Unknown')}"
        ))
    
    fig.update_layout(
        polar=dict(
            radialaxis=dict(
                visible=True,
                range=[0, 1]
            )
        ),
        title="Option Comparison (Higher is Better)",
        height=450,
        margin=dict(l=70, r=70, t=50, b=50)
    )
    
    return fig


def create_dashboard_ui():
    """Creates a Gradio UI dashboard for the tariff scenario planner."""
    with gr.Blocks(title="Tariff Scenario Planner Dashboard") as app:
        gr.Markdown("# 📊 Tariff Scenario Planner")
        
        with gr.Tabs() as tabs:
            # Analysis Tab
            with gr.TabItem("Analysis"):
                with gr.Row():
                    with gr.Column(scale=1):
                        scenario_dropdown = gr.Dropdown(
                            choices=[
                                ("Pharma: China vs. Canada", "A"),
                                ("Automotive: Mexico vs. US", "B"),
                                ("Electronics: Taiwan vs. Korea", "C"),
                                ("Custom Scenario", "Custom")
                            ],
                            label="Select Scenario",
                            value="A"
                        )
                        
                        custom_json = gr.Textbox(
                            label="Custom JSON (if selected)",
                            placeholder='{"options": [...]}',
                            lines=5,
                            visible=False
                        )
                        
                        analyze_btn = gr.Button(
                            "Run Analysis", 
                            variant="primary"
                        )
                        
                    with gr.Column(scale=2):
                        result_text = gr.Markdown(
                            label="Analysis Results"
                        )
                
                with gr.Row():
                    barplot = gr.Plot(label="Score Comparison")
                    radarplot = gr.Plot(label="Option Profile Comparison")
            
            # Details Tab
            with gr.TabItem("Detailed Results"):
                details_df = gr.DataFrame(label="Full Ranking Table")
                export_btn = gr.Button("Export Results")
                export_status = gr.Textbox(label="Export Status")
            
            # Settings Tab
            with gr.TabItem("Settings"):
                weight_cost = gr.Slider(
                    minimum=0.1, 
                    maximum=0.8, 
                    value=0.5, 
                    label="Cost Weight"
                )
                weight_risk = gr.Slider(
                    minimum=0.1, 
                    maximum=0.8, 
                    value=0.3, 
                    label="Risk Weight"
                )
                weight_leadtime = gr.Slider(
                    minimum=0.1, 
                    maximum=0.8, 
                    value=0.2, 
                    label="Lead Time Weight"
                )
                update_weights_btn = gr.Button(
                    "Update Weights",
                    variant="secondary"
                )
        
        # Event handlers
        def handle_scenario_change(scenario):
            """Handle scenario dropdown change."""
            is_custom = scenario == "Custom"
            return gr.update(visible=is_custom)
            
        scenario_dropdown.change(
            handle_scenario_change, 
            inputs=[scenario_dropdown], 
            outputs=[custom_json]
        )
        
        def handle_analysis_click(scenario, custom_data):
            """Handle analysis button click."""
            result, log = run_scenario_analysis(scenario, custom_data)
            
            # Format result text
            if "top_options" in result and result["top_options"]:
                top_option = result["top_options"][0]
                result_md = f"## Recommendation: {top_option['supplier']}\n\n"
                result_md += f"**Summary:** {top_option['summary']}\n\n"
                result_md += f"**Rationale:** {result['decision_rationale']}\n\n"
                
                # Add considerations
                result_md += "### Strategic Considerations:\n"
                for item in result["strategic_considerations"]:
                    result_md += f"- {item}\n"
            else:
                result_md = "Analysis failed. Please check your inputs."
                
            # Create visualizations
            bar_chart = create_comparison_chart(result["full_ranking"])
            radar_chart = create_radar_chart(result["top_options"])
            
            # Update details dataframe
            details_data = result["full_ranking"]
            
            return result_md, bar_chart, radar_chart, details_data
            
        analyze_btn.click(
            handle_analysis_click,
            inputs=[scenario_dropdown, custom_json],
            outputs=[result_text, barplot, radarplot, details_df]
        )
        
        def handle_export():
            """Export analysis results to CSV."""
            try:
                if not analysis_results_storage:
                    return "No analysis results to export."
                    
                df = pd.DataFrame(analysis_results_storage["full_ranking"])
                export_time = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"tariff_analysis_{export_time}.csv"
                df.to_csv(filename, index=False)
                return f"Results exported to {filename}"
            except Exception as e:
                return f"Export failed: {str(e)}"
                
        export_btn.click(
            handle_export,
            inputs=[],
            outputs=[export_status]
        )
        
    return app


# Test the functions if run directly
if __name__ == "__main__":
    result, log = run_scenario_analysis("A")
    print(f"Analysis result for scenario A: {result['decision_rationale']}")
    print(f"Top option: {result['top_options'][0]['supplier']}")
    
    # Uncomment to run the UI dashboard
    app = create_dashboard_ui()
    app.launch()
